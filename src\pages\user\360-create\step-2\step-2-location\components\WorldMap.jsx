import { useEffect, useState, useRef } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eoJSO<PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import markerIcon from "leaflet/dist/images/marker-icon.png";
import markerIcon2x from "leaflet/dist/images/marker-icon-2x.png";
import markerShadow from "leaflet/dist/images/marker-shadow.png";
import { Card } from "components/ui/Card";
import countryGeoJson from "../../../../../../assets/geojson/countries_border.json";
import CountriesList from "./CountriesList";
import "../style.css";

const WorldMap = ({ onCountryClick, setMapActiveTab }) => {
  const [countryData, setCountryData] = useState(null);
  const [countries, setCountries] = useState([]);
  const [zoomedCountry, setZoomedCountry] = useState(null);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(false);
  const mapRef = useRef(null);
  const geoJsonLayerRef = useRef(null);

  useEffect(() => {
    try {
      const transformedData = {
        type: "FeatureCollection",
        features: countryGeoJson.features.map((feature) => ({
          type: "Feature",
          properties: feature.properties,
          geometry: feature.geometry,
          id: feature.id || null,
        })),
      };
      setCountryData(transformedData);

      const countryList = countryGeoJson.features.map((feature) => ({
        name_fa: feature.properties.name_fa,
      }));
      setCountries(countryList);

      delete L.Icon.Default.prototype._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconUrl: markerIcon,
        iconRetinaUrl: markerIcon2x,
        shadowUrl: markerShadow,
      });
    } catch (error) {
      console.error("Error transforming GeoJSON:", error);
    }
  }, []);

  useEffect(() => {
    if (zoomedCountry && mapRef.current) {
      const country = countryGeoJson.features.find(
        (feature) => feature.properties.name_fa === zoomedCountry
      );

      if (country) {
        const bounds = L.geoJSON(country).getBounds();
        mapRef.current.fitBounds(bounds, { padding: [50, 50] });
      }
    }
  }, [zoomedCountry, countryGeoJson]);

  const onEachCountryFeature = (feature, layer) => {
    layer.setStyle({
      weight: 1,
      fillColor: "#D3D3D3",
      fillOpacity: 0.6,
    });

    layer.bindTooltip(
      `<span class="font-subtitle-medium">${feature.properties.name_fa}</span>`,
      {
        permanent: false,
        direction: "top",
        offset: [47, -10],
      }
    );

    layer.on("mouseover", () => {
      setHoveredCountry(feature.properties.name_fa);
      // setShouldAutoScroll(true);
      // setTimeout(() => {
      //   setShouldAutoScroll(true);
      // }, 1000);
      layer.setStyle({
        weight: 3,
        color: "#7d6cd5",
        fillColor: "#7d6cd5",
        fillOpacity: 0.7,
      });
    });

    layer.on("mouseout", () => {
      if (hoveredCountry !== feature.properties.name_fa) {
        setHoveredCountry(null);
        setShouldAutoScroll(false);
      }
      layer.setStyle({
        weight: 1,
        color: "#3388ff",
        fillColor: "#e0dfdc",
        fillOpacity: 0.4,
      });
    });

    layer.on("click", () => {
      if (mapRef.current) {
        const bounds = layer.getBounds();
        mapRef.current.fitBounds(bounds);
      }
      if (onCountryClick) {
        onCountryClick(feature.properties.name_fa);
      }
      setZoomedCountry(feature.properties.name_fa);
      setShouldAutoScroll(true);

      if (feature.properties.name_fa === "ایران" && setMapActiveTab) {
        setMapActiveTab("iran");
      }
    });
  };

  const handleHoverList = (countryName) => {
    setHoveredCountry(countryName);

    if (geoJsonLayerRef.current) {
      const layer = geoJsonLayerRef.current
        .getLayers()
        .find((layer) => layer.feature.properties.name_fa === countryName);

      if (layer) {
        layer.setStyle({
          weight: 3,
          color: "#7d6cd5",
          fillColor: "#7d6cd5",
          fillOpacity: 0.7,
        });
      }
    }
  };

  const handleMouseOutList = () => {
    if (hoveredCountry && geoJsonLayerRef.current) {
      const layer = geoJsonLayerRef.current
        .getLayers()
        .find((layer) => layer.feature.properties.name_fa === hoveredCountry);

      if (layer) {
        layer.setStyle({
          weight: 1,
          color: "#3388ff",
          fillColor: "#e0dfdc",
          fillOpacity: 0.4,
        });
      }
    }
    setHoveredCountry(null);
  };

  const handleMapClick = (countryName) => {
    if (onCountryClick) {
      onCountryClick(countryName);
    }
  };

  return (
    <Card className="flex items-center mapCard-container">
      <div className="flex !gap-5 !flex-row !w-full">
        <div className="!w-4/6">
          <MapContainer
            center={[20.0, 0.0]}
            zoom={3}
            className="map-container"
            maxBounds={[
              [-90, -180],
              [90, 180],
            ]}
            maxBoundsViscosity={1.0}
            minZoom={2}
            maxZoom={7}
            ref={mapRef}
            attributionControl={false}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
            />
            {countryData && (
              <GeoJSON
                data={countryData}
                onEachFeature={onEachCountryFeature}
                ref={geoJsonLayerRef}
              />
            )}
          </MapContainer>
        </div>
        <div className="!w-2/6">
          <CountriesList
            countries={countries}
            zoomedCountry={zoomedCountry}
            onCountryClick={handleMapClick}
            hoveredCountry={hoveredCountry}
            setHoveredCountry={setHoveredCountry}
            shouldAutoScroll={shouldAutoScroll}
            setZoomedCountry={setZoomedCountry}
            handleMouseOutList={handleMouseOutList}
            handleHoverList={handleHoverList}
          />
        </div>
      </div>
    </Card>
  );
};

WorldMap.propTypes = {
  onCountryClick: PropTypes.func,
  setMapActiveTab: PropTypes.func,
};

export default WorldMap;
