import TreeMap from "components/Charts/TreeMap";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import { toPersianNumber } from "utils/helper";

const InterestCategories = () => {
  const treeData = [
    { id: "1", name: `تگ شماره ${toPersianNumber(1)}`, value: 30 },
    { id: "2", name: `تگ شماره ${toPersianNumber(2)}`, value: 50 },
    { id: "3", name: `تگ شماره ${toPersianNumber(3)}`, value: 20 },
    {
      id: "1_1",
      name: `تگ شماره ${toPersianNumber(1.1)}`,
      value: 15,
      parent: "1",
    },
    {
      id: "1_2",
      name: `تگ شماره ${toPersianNumber(1.2)}`,
      value: 15,
      parent: "1",
    },
    {
      id: "2_1",
      name: `تگ شماره ${toPersianNumber(2.1)}`,
      value: 25,
      parent: "2",
    },
    {
      id: "2_2",
      name: `تگ شماره ${toPersianNumber(2.2)}`,
      value: 25,
      parent: "2",
    },
  ];

  const handleTreeMapClick = () => {};

  return (
    <Card className="flex flex-col gap-2">
      <Title title="دسته‌بندی علایق"></Title>
      <TreeMap treeData={treeData} handleClick={handleTreeMapClick} />
    </Card>
  );
};

export default InterestCategories;
