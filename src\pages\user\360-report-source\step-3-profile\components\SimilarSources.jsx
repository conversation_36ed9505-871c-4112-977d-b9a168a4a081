import { useState } from "react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import user from "../../../../../assets/images/360/user.png";
import { useReport360Store } from "store/report360Store";
import fallbackImg from "assets/images/default.png";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import { CaretLeft, UsersThree } from "@phosphor-icons/react";
import PopUp from "components/ui/PopUp";
import use360requestStore from "store/360requestStore";
import { useNavigate } from "react-router-dom";

const SimilarSources = () => {
  const navigate = useNavigate();
  const sourceReport = use360requestStore((state) => ({
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));

  const addProfile = useReport360Store((state) => state.addProfile);
  const similarSources = sourceReport?.profile?.similar_accounts || [];
  const platform = sourceReport.platform;
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState(null);

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
    setSelectedSource(null);
  };

  const handleShowMore = (source) => {
    addProfile({
      id: source?.id,
      user_title: source?.user_title,
      user_name: source?.user_name,
      platform: platform,
      avatar: source?.avatar,
    });
    navigate(`/app/report-360/report/source/${platform}/${source.user_name}`);
  };

  const confirmShowMore = () => {
    if (selectedSource) {
      handleShowMore(selectedSource);
    }
    closeConfirmPopup();
  };

  let platformName;
  switch (platform) {
    case "twitter":
      platformName = "اکانت توییتر";
      break;
    case "instagram":
      platformName = "اکانت اینستاگرام";
      break;
    case "telegram":
      platformName = "اکانت تلگرام";
      break;
    default:
      platformName = "خبرگزاری";
  }

  return (
    <>
      <Card className="flex flex-col gap-2 h-full !w-[99.5%]">
        <Title title="منابع مشابه" />
        {similarSources?.length > 0 ? (
          <Swiper
            spaceBetween={10}
            navigation={true}
            modules={[Navigation]}
            slidesPerView={5}
            loop={false}
            className="swiper-container h-full px-5 pt-4 w-full"
          >
            {similarSources?.map((source, i) => (
              <SwiperSlide
                key={source.id || i}
                className="swiper-slide flex bg-white"
              >
                <div
                  className="flex flex-col w-full !items-center h-full text-center py-3 bg-white transition duration-300 delay-100 cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setSelectedSource(source);
                    setIsPopupOpen(true);
                  }}
                >
                  <div
                    className={"rounded-[50px] w-16 h-16"}
                    style={{
                      backgroundImage: `url(${
                        source.avatar || user
                      }), url(/logo_small.png)`,
                      backgroundRepeat: "no-repeat",
                      backgroundSize: "cover",
                      backgroundPosition: "center center",
                    }}
                  ></div>
                  <h3 className="font-subtitle-large pt-1 truncate w-full">
                    {source.user_title || `نام ${platformName}`}
                  </h3>
                  <span className="font-overline-medium truncate w-full">
                    {source.user_name || `${platform}-ID`}@
                  </span>
                  <span className="font-overline-medium pt-1 flex items-center gap-1">
                    {toPersianNumber(formatShortNumber(source.follower_count))}
                    <UsersThree size={16} />
                  </span>
                  <div className="flex mt-5 justify-center items-center gap-1 duration-200 p-2 text-[#6F5CD1] rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]">
                    <span className="font-overline-small">مشاهده استعلام</span>
                    <CaretLeft size={12} />
                  </div>
                </div>
                {i < similarSources.length - 1 && (
                  <div className="w-px bg-gray-200 h-full self-stretch flex-shrink-0"></div>
                )}
              </SwiperSlide>
            ))}
          </Swiper>
        ) : (
          <div className="h-full flex items-center justify-center font-subtitle-medium">
            داده ای برای نمایش وجود ندارد
          </div>
        )}
      </Card>

      <PopUp
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={confirmShowMore}
        title={`آیا می‌خواهید وارد گزارش ۳۶۰ ${selectedSource?.user_title} شوید؟`}
        agreeButton="بله"
        cancleButton="خیر"
      >
        <p className="py-5 font-body-medium">
          با کلیک برروی گزینه بله گزارش منبع جدید در لیست پروفایل‌های باز شده
          اضافه می‌شود
        </p>
      </PopUp>
    </>
  );
};

export default SimilarSources;
