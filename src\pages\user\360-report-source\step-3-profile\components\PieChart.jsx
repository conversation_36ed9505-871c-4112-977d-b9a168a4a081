import { useEffect, useRef } from "react";
import Highcharts from "highcharts";
import { toPersianNumber } from "utils/helper";
import PropTypes from "prop-types";

const PieChart = ({ data }) => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      Highcharts.chart(chartRef.current, {
        chart: {
          type: "pie",
        },
        title: {
          text: null,
        },
        tooltip: {
          formatter: function () {
            return `<b>%${this.point.name}</b>: ${toPersianNumber(
              this.percentage.toFixed(1)
            )}`;
          },
        },
        subtitle: {
          text: null,
        },
        credits: {
          enabled: false,
        },
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: true,
              formatter: function () {
                return this.percentage > 9
                  ? `${toPersianNumber(this.percentage.toFixed(1))}%`
                  : null;
              },
              style: {
                fontSize: "1em",
                textOutline: "none",
                opacity: 2,
              },
              distance: -50,
            },
          },
          series: {
            // allowPointSelect: true,
            // cursor: "pointer",
          },
        },
        series: [
          {
            name: null,
            colorByPoint: true,
            data: data.map((item) => ({
              name: item.name,
              y: item.y,
              color: item.color,
              sliced: item.sliced || false,
              selected: item.selected || false,
            })),
          },
        ],
      });
    }
  }, [data]);

  return <div ref={chartRef} style={{ width: "95%", height: "95%" }} />;
};

PieChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      y: PropTypes.number.isRequired,
      color: PropTypes.string,
      sliced: PropTypes.bool,
      selected: PropTypes.bool,
    })
  ).isRequired,
};

export default PieChart;
