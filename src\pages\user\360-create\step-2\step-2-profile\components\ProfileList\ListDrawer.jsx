import {
  Calendar,
  File,
  Heart,
  Image,
  MapPin,
  Network,
  Note,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  User,
  UsersThree,
} from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge";
import PropTypes from "prop-types";
import { formatShortNumber, toPersianNumber } from "utils/helper";
// import ListDrawerHeaderMenu from "pages/user/hot-topic/components/ListDrawerTopic/ListDrawerHeaderMenu.jsx";
import fallbackImage from "/logo_small.png";

const ListDrawer = ({
  children,
  media,
  data,
  showHeaderMenu = true,
  height,
  handleClickOnAvatar,
  selectable = false,
  fallbackImg,
  isCentralNodeSelected,
  handleSelect,
  isSelected,
  onShowDrawer = false,
  className,
}) => {
  const typeTranslator = {
    channel: "کانال",
    group: "گروه",
  };

  return (
    <div
      className={`p-4 flex flex-col gap-5 min-h-40 w-full [direction:rtl] ${className}`}
      style={{ ...(height ? { height } : {}) }}
    >
      <div className="flex justify-between">
        <div className="flex gap-4 items-center">
          {selectable && (
            <input
              type="checkbox"
              className="size-6"
              style={{ accentColor: "#343330" }}
              checked={isSelected}
              onChange={(e) => handleSelect(e.target.checked)}
            />
          )}
          <div className="flex gap-2 relative">
            <img
              src={data?.avatar || data?.logo_image || "/logo_small.png"}
              className={"size-10 rounded-full bg-contain ml-2"}
              onClick={handleClickOnAvatar}
              onError={(e) => (e.target.src = fallbackImg)}
            />
            {/* <div
              className={"size-10 rounded-full bg-contain ml-2"}
              style={{
                backgroundImage: `url(${
                  data?.avatar ||
                  data?.original_avatar ||
                  data.logo_image ||
                  fallbackImg
                })`,
                backgroundRepeat: "no-repeat",
                backgroundSize: "contain",
                backgroundPosition: "center center",
                cursor: handleClickOnAvatar ? "zoom-in" : "default",
              }}
              onClick={handleClickOnAvatar}
            ></div> */}
            {showHeaderMenu && (
              <span
                className={
                  "absolute top-6 right-0 rounded-full overflow-hidden !w-[20px] !h-[20px]"
                }
              >
                <MediaBadge media={media} className={"!h-[20px] !w-[20px]"} />
              </span>
            )}
            <div className="flex flex-col flex-1">
              <span className="font-subtitle-medium text-light-neutral-text-high">
                {data?.title ||
                  data?.full_name ||
                  data?.user_title ||
                  data?.channel_title ||
                  "کاربر"}
              </span>
              <span className="font-overline-medium text-light-neutral-text-medium">
                {media === "news"
                  ? data?.base_url
                  : data?.user_name
                  ? data?.user_name + "@"
                  : data?.channel_username
                  ? data?.channel_username + "@"
                  : ""}
              </span>
            </div>
          </div>
        </div>
        {!isCentralNodeSelected && onShowDrawer && (
          <div>
            <button
              onClick={onShowDrawer}
              className="font-body-medium shadow bg-light-neutral-background-medium rounded-lg p-2 flex justify-center gap-2 items-center"
            >
              جزئیات ارتباط
              <Network size={16} />
            </button>
          </div>
        )}
      </div>
      <div className="font-body-medium">{children}</div>
      <div className="flex justify-evenly gap-4">
        <div className="flex flex-col justify-center">
          <span className="font-subtitle-large text-center">
            {media === "news"
              ? data?.office_location
              : media === "telegram"
              ? typeTranslator[data?.type]
              : toPersianNumber(formatShortNumber(data?.follower_count ?? 0))}
          </span>
          <div className="flex text-[#8f8f8f] justify-center items-center gap-2">
            <UsersThree />
            <span className="font-body-small text-center">
              {media === "news"
                ? "محل ستاد"
                : media === "telegram"
                ? "نوع"
                : "فالوور"}
            </span>
          </div>
        </div>
        {media === "twitter" && (
          <>
            <div className="border-r border-gray-300"></div>
            <div className="flex flex-col justify-center">
              <span className="font-subtitle-large text-center">
                {toPersianNumber(formatShortNumber(data?.following_count ?? 0))}
              </span>
              <div className="flex text-[#8f8f8f] justify-center items-center gap-2">
                <User />
                <span className="font-body-small text-center">فالووینگ</span>
              </div>
            </div>
          </>
        )}

        <div className="border-r border-gray-300"></div>
        <div className="flex flex-col justify-center">
          <span className="font-subtitle-large text-center">
            {media === "news"
              ? data?.founder ?? "-"
              : media === "telegram"
              ? toPersianNumber(formatShortNumber(data?.member_count ?? 0)) ||
                "-"
              : toPersianNumber(formatShortNumber(data?.tweet_count ?? 0)) ||
                "-"}
          </span>
          <div className="flex text-[#8f8f8f] justify-center items-center gap-2">
            {media === "telegram" ? <User /> : <NotePencil />}
            <span className="font-body-small text-center">
              {media === "news"
                ? "صاحب امتیاز"
                : media === "telegram"
                ? "تعداد اعضا"
                : "تعداد توئیت"}
            </span>
          </div>
        </div>
        <div className="border-r border-gray-300"></div>
        <div className="flex flex-col justify-center">
          <span className="font-subtitle-large text-center">
            {media === "news"
              ? data?.founded_year
              : media === "telegram"
              ? toPersianNumber(formatShortNumber(data?.file_count))
              : toPersianNumber(formatShortNumber(data?.like_count ?? 0)) ||
                "-"}
          </span>
          <div className="flex text-[#8f8f8f] justify-center items-center gap-2">
            {media === "news" ? (
              <Calendar />
            ) : media === "telegram" ? (
              <File />
            ) : (
              <Heart />
            )}
            <span className="font-body-small text-center">
              {media === "news"
                ? "سال تاسیس"
                : media === "telegram"
                ? "تعداد فایل"
                : "تعداد لایک"}
            </span>
          </div>
        </div>
        <div className="border-r border-gray-300"></div>
        <div className="flex flex-col justify-center">
          <span className="font-subtitle-large text-center">
            {media === "telegram"
              ? toPersianNumber(
                  formatShortNumber(data?.photo_count + data?.video_count)
                )
              : data?.location || "-"}
          </span>
          <div className="flex text-[#8f8f8f] justify-center items-center gap-2">
            {media === "telegram" ? <Image /> : <MapPin />}
            <span className="font-body-small text-center">
              {media === "telegram" ? "تعداد محتوای تصویری" : "موقعیت"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListDrawer;

ListDrawer.propTypes = {
  children: PropTypes.node,
  media: PropTypes.oneOf(["telegram", "twitter", "instagram", "eitaa", "news"])
    .isRequired,
  data: PropTypes.object.isRequired,
  showMediaName: PropTypes.bool,
  showHeaderMenu: PropTypes.bool,
  height: PropTypes.string,
  handleClickOnAvatar: PropTypes.func,
  selectable: PropTypes.bool,
  handleSelect: PropTypes.func,
  isSelected: PropTypes.bool,
  fallbackImg: PropTypes.string,
  showBookMark: PropTypes.bool,
  isCentralNodeSelected: PropTypes.bool,
  setMyBookmarksLists: PropTypes.func,
  hasNotes: PropTypes.bool,
  is_pin: PropTypes.bool,
  onShowDrawer: PropTypes.func,
  setUpdater: PropTypes.func,
  className: PropTypes.string,
};
