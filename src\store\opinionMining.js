import { create } from "zustand";

const useOpinionMiningStore = create((set) => ({
  opinionMiningData: {
    type: "",
    title: "",
    description: "",
    opinion_platform: "twitter",
    opinion_type: "customized",
    filterList: [],
    query: {
      platform: {
        twitter: {
          sentiment: ["positive", "negative", "neutral"],
          adv: ["adv", "non-adv"],
          hashtags: [],
        },
      },
    },
  },
  setOpinionMiningData: (data) => set({ opinionMiningData: data }),
  updateOpinionMiningData: (updates) =>
    set((state) => ({
      opinionMiningData: { ...state.opinionMiningData, ...updates },
    })),
  clearOpinionMiningData: () =>
    set(() => ({
      opinionMiningData: {
        type: "",
        title: "",
        description: "",
        opinion_platform: "twitter",
        opinion_type: "customized",
        filterList: [],
        query: {
          platform: {
            twitter: {
              sentiment: ["positive", "negative", "neutral"],
              adv: ["adv", "non-adv"],
              hashtags: [],
            },
          },
        },
      },
    })),
}));

export default useOpinionMiningStore;
