import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper";

const PieChart = ({
  height,
  data,
  colors,
  showLabelsInTooltip = false,
  legend = false,
}) => {
  const sortedData = data
    ? (() => {
        const sorted = [...data].sort((a, b) => b.y - a.y);

        const top7 = sorted.slice(0, 7);

        const otherSum = sorted.slice(7).reduce((sum, item) => sum + item.y, 0);

        return otherSum > 0 ? [...top7, { name: "سایر", y: otherSum }] : top7;
      })()
    : [];

  const options = {
    chart: {
      type: "pie",
      height: 240,
      backgroundColor: "transparent",
      style: {
        filter: "none",
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ["downloadPNG"],
          x: 0,
          y: -10,
        },
      },
    },
    plotOptions: {
      pie: {
        showInLegend: true,
        borderRadius: 9,
        borderWidth: 4,
        innerSize: "50%",
        center: ["50%", "50%"],
      },
    },
    title: {
      text: null,
    },
    tooltip: {
      useHTML: true,
      formatter: function () {
        if (showLabelsInTooltip) {
          return `<b style="font-family:iranyekan">${
            this.point.name
          }: ${toPersianNumber(this.y)}</b>`;
        }
        return `<b style="font-family:iranyekan">${
          this.point.name
        }: ${toPersianNumber(this.y)} </b>`;
      },
    },
    colors: colors,
    series: [
      {
        name: null,
        data: sortedData,
        innerSize: "40%",
        dataLabels: {
          enabled: false,
        },
        showInLegend: true,
      },
    ],
    credits: {
      enabled: false,
    },
    legend: {
      enabled: legend ? true : false,
      align: "right",
      verticalAlign: "middle",
      layout: "vertical",
      maxHeight: 340,
      useHTML: true,
      labelFormatter: function () {
        return `<div style="font-family: iranyekan;">${
          this.name
        }:   %${toPersianNumber(Math.round(this.percentage))} </div>`;
      },
      x: 15,
      itemMarginTop: 5,
      itemMarginBottom: 5,
    },
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        // height: 170,
        alignItems: "center",
        justifyContent: "center",
        width: "95%",
      }}
      className="font-body-medium"
    >
      <div
        style={{ flex: 1 }}
        className="justify-center !items-center h-full !w-full"
      >
        <HighchartsReact highcharts={Highcharts} options={options} />
      </div>
    </div>
  );
};

PieChart.propTypes = {
  height: PropTypes.number,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      y: PropTypes.number.isRequired,
    })
  ).isRequired,
  colors: PropTypes.arrayOf(PropTypes.string).isRequired,
  showLabelsInTooltip: PropTypes.bool,
  legend: PropTypes.bool,
};

export default PieChart;
