import { useEffect, useState, useRef } from "react";
import PropTypes from "prop-types";
import { SpinnerGap } from "@phosphor-icons/react";
import { buildRequestData, transformResponseData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import SummaryCardTopic from "pages/user/hot-topic/components/SummaryCardTopic";
import { findPlatform } from "pages/user/hot-topic/utils";
import DropDown from "components/ui/DropDown";
import { toPersianNumber } from "utils/helper";

const WordContent = ({ word, source, platform, date }) => {
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [error, setError] = useState(null);
  const [visibleItems, setVisibleItems] = useState(5);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [sortOrder, setSortOrder] = useState("نزولی");
  const loadMoreRef = useRef(null);

  const loadMoreItems = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setVisibleItems((prev) => prev + 5);
      setLoadingMore(false);
    }, 1000);
  };

  const sortedResults = [...searchResults].sort((a, b) => {
    const dateA = new Date(a.time || a.date || 0);
    const dateB = new Date(b.time || b.date || 0);

    if (sortOrder === "نزولی") {
      return dateB - dateA;
    } else {
      return dateA - dateB;
    }
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (
          entry.isIntersecting &&
          !loadingMore &&
          visibleItems < sortedResults.length
        ) {
          loadMoreItems();
        }
      },
      { threshold: 1.0 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loadingMore, visibleItems, sortedResults.length]);

  useEffect(() => {
    const fetchWordContent = async () => {
      if (!word) return;

      setLoading(true);
      setError(null);

      try {
        const requestData = buildRequestData(
          {
            platform,
            sources: [source],
            q: word,
            page: page,
            date,
          },
          "advance"
        );

        const responseStinas = await advanceSearch.search(requestData);
        const stinasData = responseStinas?.data?.data;

        const allResults = [];
        if (stinasData) {
          Object.keys(stinasData).forEach((platform) => {
            if (Array.isArray(stinasData[platform])) {
              const transformedData = transformResponseData(
                stinasData[platform]
              );
              allResults.push(...transformedData);
            }
          });
        }

        setSearchResults(allResults);
        setVisibleItems(5);
        setTotal(stinasData.total || 0);
      } catch (error) {
        console.error("Error fetching word content:", error);
        setError("خطا در دریافت اطلاعات");
        setSearchResults([]);
        setVisibleItems(5);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    };

    fetchWordContent();
  }, [word]);

  useEffect(() => {
    setVisibleItems(5);
  }, [sortOrder]);

  if (loading) {
    return (
      <div className="flex w-full h-64 justify-center items-center">
        <SpinnerGap size={40} className="animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full h-64 justify-center items-center">
        <div className="text-red-500 font-subtitle-medium">{error}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full gap-4 bg-gray-50">
      <div className="bg-[#B4ABE34D] rounded-lg p-2">
        <h2 dir="ltr" className="font-headline-large text-center">
          &quot;{word}&quot;
        </h2>
        {searchResults.length > 0 && (
          <p className="text-sm text-gray-500 mt-2 font-body-medium text-center">
            {toPersianNumber(total)} بار
          </p>
        )}
      </div>

      {searchResults.length > 0 && (
        <div className="px-4 pt-2">
          <div className="flex justify-between items-center">
            <span className="font-subtitle-large">محتوای نوشته شده</span>
            <div className="w-48">
              <DropDown
                title="مرتب‌سازی"
                subsets={["نزولی", "صعودی"]}
                selected={sortOrder}
                setSelected={setSortOrder}
              />
            </div>
          </div>
        </div>
      )}

      <div className="flex-1 overflow-y-auto px-3 py-2">
        {searchResults.length === 0 ? (
          <div className="flex w-full h-64 justify-center items-center">
            <div className="text-center">
              <div className="font-subtitle-medium text-gray-500">
                محتوایی برای این کلمه یافت نشد
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedResults.slice(0, visibleItems).map((data, index) => (
              <SummaryCardTopic
                key={index}
                media={findPlatform(data)}
                data={data}
                word={word}
                platform={data.platform || findPlatform(data)}
              />
            ))}

            {loadingMore && (
              <div className="flex justify-center py-4">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            )}

            <div ref={loadMoreRef} className="h-1" />
          </div>
        )}
      </div>
    </div>
  );
};

WordContent.propTypes = {
  word: PropTypes.string.isRequired,
  date: PropTypes.object,
  source: PropTypes.string,
  platform: PropTypes.platform,
};

export default WordContent;
