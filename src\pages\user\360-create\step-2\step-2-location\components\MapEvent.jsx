import { useEffect } from "react";
import { useMapEvents } from "react-leaflet";

const MapEventsHandler = ({ setZoomLevel, setTileMode }) => {
  const map = useMapEvents({
    zoomend: () => {
      const zoom = map.getZoom();
      setZoomLevel(zoom);
      // setTileMode(zoom >= 10 ? "satellite" : "street");
    },
  });

  useEffect(() => {
    const initialZoom = map.getZoom();
    setZoomLevel(initialZoom);
    // setTileMode(initialZoom >= 10 ? "satellite" : "street");
  }, [map, setZoomLevel, setTileMode]);

  return null;
};

export default MapEventsHandler;
