import { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import "../../style.css";
import { formatShortNumber, toPersianNumber } from "utils/helper.js";
import advanceSearch from "service/api/advanceSearch.js";
import { buildRequestData } from "utils/requestData.js";
import { useReport360Store } from "store/report360Store.js";
import PieChart from "./PieChart.jsx";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const getPersianLabel = (key) => {
  const labels = {
    post: "توییت",
    repost: "ریتوییت",
    qoute: "کوت",
    reply: "ریپلای",
  };
  return labels[key] || key;
};

const getColorByKey = (key) => {
  const colors = {
    post: "#432FA7",
    repost: "#9A91CE",
    qoute: "gray",
    reply: "black",
  };
  return colors[key] || "#cccccc";
};

const PersonContentNumber = ({ activePlatform }) => {
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info?.entity,
    content_type_distribution:
      state.report?.content?.report_info?.content_type_distribution,
  }));

  const data = sourceReport.content_type_distribution?.[activePlatform] || [];

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Content Type",
      data: data.map(({ count }) => count),
      time: data.map(({ key }) => getPersianLabel(key)),
    },
  ];

  const time = data.map(({ key }) => getPersianLabel(key));

  const pieChartData = data.map((item) => ({
    name: getPersianLabel(item.key),
    y: item.count,
    color: getColorByKey(item.key),
    // sliced: item.key === "repost",
    // selected: item.key === "repost",
  }));

  return (
    <Card className="card-animation flex-1 card-delay !h-full">
      <div className="flex flex-col gap-2 w-full">
        <div className="flex items-center justify-between">
          <Title title="تعداد محتوای منتشر شده" />
          <ExportMenu
            chartSelector=".person-content-number-container"
            fileName="person-content-number"
            series={series}
            time={time}
            excelHeaders={["Content Type", "Count"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            chartTitle="تعداد محتوای منتشر شده"
          />
        </div>
        <div className="w-full person-content-number-container flex flex-col px-3 h-full items-center justify-center">
          {data.length > 0 ? (
            <>
              <div className="w-[70%] flex justify-center">
                <PieChart data={pieChartData} />
              </div>
              <div className="grid grid-cols-2 gap-x-20 gap-y-4 justify-center">
                {data.slice(0, 4).map((item, i) => (
                  <div key={i} className="flex gap-2 justify-between">
                    <div className="flex items-center font-body-small gap-2">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: getColorByKey(item.key) }}
                      ></div>
                      {getPersianLabel(item.key)}
                    </div>
                    <div>{`(${toPersianNumber(
                      formatShortNumber(item?.count)
                    )})`}</div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="h-full flex items-center justify-center font-subtitle-medium">
              داده‌ای برای نمایش وجود ندارد
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

PersonContentNumber.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default memo(PersonContentNumber);
