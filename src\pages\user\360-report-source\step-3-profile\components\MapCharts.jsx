import { Card } from "components/ui/Card";
import { CTabs } from "components/ui/CTabs";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import MapTest from "./iranMap";
import WorldMap from "./worldMap";
import { useState } from "react";
import "../../style.css";
import { toPersianNumber } from "utils/helper.js";

const MapCharts = () => {
  const [mapActiveTab, setMapActiveTab] = useState("iran");

  const countries = [
    { country: "کرمان", percent: toPersianNumber(44) },
    { country: "خراسان رضوی", percent: toPersianNumber(37) },
    { country: "کردستان", percent: toPersianNumber(52) },
    { country: "البرز", percent: toPersianNumber(29) },
    { country: "ارومیه", percent: toPersianNumber(61) },
  ];

  const onMapTabChange = (name) => {
    setMapActiveTab(name);
  };

  return (
    <div className="flex w-full h-full" style={{ fontFamily: "iranyekan" }}>
      <Card className="px-0 w-full card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <Title title="مکان زندگی" />
            <div className="flex">
              <CTabs
                tabArray={[
                  { id: "iran", title: "ایران" },
                  { id: "world", title: "جهان" },
                ]}
                activeTab={mapActiveTab}
                onChange={onMapTabChange}
              />
            </div>
          </div>
          <div className="flex w-full gap-4 px-3 items-center">
            <div className="w-1/3 flex flex-col gap-4 pl-4">
              {countries.map((item, i) => (
                <div key={i} className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-[#292FB8]"></div>
                    {item.country}
                  </div>
                  <div>{item.percent}%</div>
                </div>
              ))}
            </div>
            <div className="w-2/3">
              {mapActiveTab === "iran" && <MapTest />}
              {mapActiveTab === "world" && <WorldMap />}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MapCharts;
