import PropTypes from "prop-types";
import PersonBestSource from "../../components/BestSource";
import PersonContentNumber from "../../components/ContentNumber";
import LatestContent from "../../components/LatestContent";
// import OwnedResources from "../../components/OwnedResources";
import PersonReleaseProcess from "../../components/ReleaseProcess";
import PersonRepeatHashtags from "../../components/RepeatHashtags";
import ResourcesInfo from "../../components/ResourcesInfo";
import PersonThematicCategory from "../../components/ThematicCategory";
import PersonSentiment from "../../components/Sentiment";
import PersonEntities from "../../components/entities";
import PersonRepeatWords from "../../components/RepeatWords";
// import PersonAge from "../../components/Age";
// import PersonGender from "../../components/gender";

const TwitterBased = () => {
  return (
    <div className="flex flex-col gap-3 px-4">
      {/* <OwnedResources activeSpan={activeSpan} /> */}
      <ResourcesInfo activePlatform={"twitter"} />
      <LatestContent activePlatform={"twitter"} />
      <div className="flex gap-3 items-stretch">
        <div className="w-4/6">
          <PersonReleaseProcess activePlatform={"twitter"} />
        </div>
        <div className="w-2/6">
          <PersonContentNumber activePlatform={"twitter"} />
        </div>
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="w-2/6">
          <PersonSentiment activePlatform={"twitter"} />
        </div>
        <div className="w-4/6">
          <PersonThematicCategory activePlatform={"twitter"} />
        </div>
        {/* <div className="flex-1 min-w-0">
          <PersonAge />
        </div>
        <div className="flex-1 min-w-0">
          <PersonGender />
        </div> */}
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="w-3/5">
          <PersonBestSource activePlatform={"twitter"} />
        </div>
        <div className="w-2/5">
          <PersonEntities activePlatform={"twitter"} />
        </div>
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="w-[50%] h-full">
          <PersonRepeatHashtags activePlatform={"twitter"} />
        </div>
        <div className="w-[50%] h-full">
          <PersonRepeatWords activePlatform={"twitter"} />
        </div>
      </div>
    </div>
  );
};

export default TwitterBased;
