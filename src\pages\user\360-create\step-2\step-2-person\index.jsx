import { useEffect, useState } from "react";
import SearchBar from "../step-2-profile/components/SearchBar";
import PersonList from "./components/PersonList";
import PropTypes from "prop-types";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { buildRequestData } from "utils/requestData";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";
import { useLayoutContext } from "context/layout-context";
import { useNavigate } from "react-router-dom";

const Step2Person = () => {
  const navigate = useNavigate();

  const [queryValue, setQueryValue] = useState("");
  const { setBreadcrumb } = useLayoutContext();
  const breadcrumbList = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "استعلام موجودیت", link: "/app/report-360/create" },
    { title: queryValue || "جست‌وجو" },
  ];
  useBreadcrumb(breadcrumbList);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [dataArray, setDataArray] = useState([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(null);
  const { date, entity } = useReport360Store((state) => state.report);

  const getSearchData = async (q, abortController = null) => {
    if (loading) return null;
    if (!q || q === "") {
      setDataArray([]);
      return null;
    }
    setLoading(true);
    setQueryValue(q);
    try {
      const requestData = buildRequestData(
        {
          date,
          q: q,
          platform: "all",
          page: page,
        },
        "similar_phrases",
        12,
        true
      );

      const responseStinas = await advanceSearch.search(
        requestData,
        abortController
      );

      const stinasData = responseStinas?.data?.data?.phrases;
      setDataArray(stinasData);
      setTotal(responseStinas?.data?.data?.total);
    } catch (error) {
      console.log(error);
      setDataArray([]);
    } finally {
      setLoading(false);
    }
  };

  const triggerSearch = (q) => {
    setSearchValue(q);
    setPage(1);
  };

  useEffect(() => {
    const abortController = new AbortController();
    getSearchData(searchValue, abortController);
    setBreadcrumb(breadcrumbList);

    return () => abortController.abort();
  }, [searchValue, page, date, entity]);

  const nextStep = (q) => {
    navigate(`/app/report-360/report/entity/${q}`);
  };

  return (
    <div className="h-full w-full pt-6 [direction:rtl]">
      <SearchBar
        search={triggerSearch}
        loading={loading}
        setLoading={setLoading}
        searchValue={searchValue}
        isFromEntity={true}
      />
      <PersonList
        page={page}
        setPage={setPage}
        nextStep={nextStep}
        data={dataArray}
        loading={loading}
        total={total}
      />
    </div>
  );
};

Step2Person.propTypes = {
  nextStep: PropTypes.func.isRequired,
};

export default Step2Person;
