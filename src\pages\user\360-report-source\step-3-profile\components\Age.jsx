import Bar from "components/Charts/Bar";
import { Card } from "components/ui/Card";
import Divider from "components/ui/Divider";
import { fixPercentToShow } from "utils/helper";
import Kid from "../../../../../assets/images/360/age/Kid.svg";
import Young from "../../../../../assets/images/360/age/Young.svg";
import Old from "../../../../../assets/images/360/age/Old.svg";
import MiddleAge from "../../../../../assets/images/360/age/MiddleAge.svg";
import '../../style.css'

const Age = () => {
  const staticData = {
    teen: 25,
    young: 30,
    adult: 35,
    old: 10,
  };

  return (
    <Card className="flex flex-col justify-between gap-4 card-animation card-delay">
      <p className="font-subtitle-large text-right">دسته‌بندی سن</p>
      <Divider />
      <Bar data={staticData} />
      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <div className="font-body-medium text-[#43B3E4] flex items-center gap-1">
            <img src={Kid} alt="icon" />
            <p>کودک</p>
          </div>
          <div className="font-body-large">
            {fixPercentToShow(String(staticData.teen.toFixed(2) / 100))}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="font-body-medium text-[#2A7DE0] flex items-center gap-1">
            <img src={Young} alt="icon" />
            <p>جوان</p>
          </div>
          <div className="font-body-large">
            {fixPercentToShow(String(staticData.young.toFixed(2) / 100))}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="font-body-medium text-[#3144EF] flex items-center gap-1">
            <img src={MiddleAge} alt="icon" />
            <p>میانسال</p>
          </div>
          <div className="font-body-large">
            {fixPercentToShow(String(staticData.adult.toFixed(2) / 100))}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="font-body-medium text-[#5911CF] flex items-center gap-1">
            <img src={Old} alt="icon" />
            <p>سالخورده</p>
          </div>
          <div className="font-body-large">
            {fixPercentToShow(String(staticData.old.toFixed(2) / 100))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default Age;
