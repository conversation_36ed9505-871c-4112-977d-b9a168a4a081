import {
  SquaresFour,
  Rss,
  Telegram<PERSON>ogo,
  InstagramLogo,
  XLogo,
} from "@phosphor-icons/react";
import PropTypes from "prop-types";
import PLATFORMS from "constants/platforms.js";

const PlatformTabs = ({ activeTab, setActiveTab, setPage }) => {
  const tabArray = [
    { id: "all", title: "همه", icon: SquaresFour, color: "#000" },
    {
      id: PLATFORMS.TWITTER,
      title: "ایکس (توئیتر)",
      icon: XLogo,
    },
    {
      id: PLATFORMS.INSTAGRAM,
      title: "اینستاگرام",
      icon: InstagramLogo,
    },
    {
      id: PLATFORMS.TELEGRAM,
      title: "تلگرام",
      icon: TelegramLogo,
    },
    {
      id: PLATFORMS.NEWS,
      title: "وب‌سایت خبری",
      icon: Rss,
    },
  ];

  return (
    <ul className="flex flex-row w-full align-middle py-1 font-body-medium text-light-neutral-text-medium text-center">
      {tabArray.map(({ id, title, icon: Icon }) => (
        <li key={id} className="mx-1">
          <div
            aria-current="page"
            className={`flex justify-center items-center cursor-pointer
                   w-full h-full px-2 py-1 border-b-2 border-light-primary-text-rest
                    ${
                      activeTab === id
                        ? "text-light-primary-text-rest active"
                        : "!border-b-0 border-none"
                    }`}
            onClick={() => {
              setActiveTab(id);
              setPage(1);
            }}
          >
            {Icon && <Icon className="inline-block h-full ml-1" size={16} />}
            <div className={"flex text-center gap-1"}>
              <span>{title}</span>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );
};

PlatformTabs.propTypes = {
  activeTab: PropTypes.shape({}),
  setActiveTab: PropTypes.func.isRequired,
};

export default PlatformTabs;
