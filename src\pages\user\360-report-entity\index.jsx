import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import Step3Person from "./step-3-person";

const Report360Entity = () => {
  let breadcrumbConditional = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "گزارش جدید" },
  ];
  const breadcrumbList = breadcrumbConditional;
  useBreadcrumb(breadcrumbList);

  return (
    <div className="flex-1">
      <Step3Person />
    </div>
  );
};

export default Report360Entity;
