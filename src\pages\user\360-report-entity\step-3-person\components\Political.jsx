import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title";

const PersonPoliticalSpectrum = () => {
  const tabs = [
    {
      id: "wordCloud",
      title: "حامیان انقلاب اسلامی",
      color: "rgba(0, 123, 255, 1)",
    },
    { id: "cluster", title: "برانداز", color: "rgba(128, 128, 128, 1)" },
    { id: "cluster", title: "سلطنت", color: "rgba(128, 128, 128, 1)" },
    { id: "cluster", title: "مستقل", color: "rgba(128, 128, 128, 1)" },
    {
      id: "cluster",
      title: "فعالین مدنی غرب‌گرا",
      color: "rgba(128, 128, 128, 1)",
    },
    { id: "cluster", title: "منافقین", color: "rgba(128, 128, 128, 1)" },
    { id: "cluster", title: "احمدی نژادی", color: "rgba(128, 128, 128, 1)" },
  ];

  return (
    <Card className="h-full w-full">
      <div>
        <Title title="طیف سیاسی"></Title>
        <div className="w-full mt-6 px-4 font-overline-medium flex flex-wrap gap-4">
          {tabs.map((tab, i) => (
            <span
              key={i}
              className="py-1 px-3 border rounded-md transition duration-300 hover:bg-[#9BEFE9]"
              style={{
                background: `linear-gradient(to left, ${tab.color} 0%, ${tab.color} 10%, rgba(255, 255, 255, 0) 100%)`,
              }}
            >
              {tab.title}
            </span>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default PersonPoliticalSpectrum;
