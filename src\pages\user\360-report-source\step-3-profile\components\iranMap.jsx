import { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsMap from "highcharts/modules/map";

HighchartsMap(Highcharts);

const MapTest = () => {
  const [topology, setTopology] = useState(null);

  useEffect(() => {
    const fetchTopology = async () => {
      try {
        const response = await fetch(
          "https://code.highcharts.com/mapdata/countries/ir/ir-all.topo.json"
        );
        const topoData = await response.json();
        setTopology(topoData);
      } catch (error) {
        console.error("Error fetching the map data:", error);
      }
    };

    fetchTopology();
  }, []);

  useEffect(() => {
    if (topology) {
      const data = [
        ["ir-5428", 10],
        ["ir-hg", 11],
        ["ir-bs", 12],
        ["ir-kb", 13],
        ["ir-fa", 14],
        ["ir-es", 15],
        ["ir-sm", 16],
        ["ir-go", 17],
        ["ir-mn", 18],
        ["ir-th", 19],
        ["ir-mk", 20],
        ["ir-ya", 21],
        ["ir-cm", 22],
        ["ir-kz", 23],
        ["ir-lo", 24],
        ["ir-il", 25],
        ["ir-ar", 26],
        ["ir-qm", 27],
        ["ir-hd", 28],
        ["ir-za", 29],
        ["ir-qz", 30],
        ["ir-wa", 31],
        ["ir-ea", 32],
        ["ir-bk", 33],
        ["ir-gi", 34],
        ["ir-kd", 35],
        ["ir-kj", 36],
        ["ir-kv", 37],
        ["ir-ks", 38],
        ["ir-sb", 39],
        ["ir-ke", 40],
        ["ir-al", 41],
      ];

      Highcharts.mapChart("container", {
        chart: {
          map: topology,
        },

        title: {
          text: null,
        },

        credits: {
          enabled: false,
        },

        subtitle: {
          enabled: false,
        },

        colorAxis: {
          min: 0,
        },

        series: [
          {
            data: data,
            name: "city:",
            states: {
              hover: {
                color: "#6D72E5",
              },
            },
            nullColor: "#D1D4DD",
            dataLabels: {
              enabled: false, 
            },
            point: {
              events: {
                mouseOver: function () {
                  this.update({
                    dataLabels: {
                      enabled: true,
                      format: this.name, 
                    },
                  });
                },
                mouseOut: function () {
                  this.series?.data?.forEach(point => {
                    point.update({
                      dataLabels: {
                        enabled: false,
                      },
                    });
                  });
                },
              },
            },
          },
        ],
      });
    }
  }, [topology]);

  return <div id="container" style={{ height: "500px", width: "100%" }} />;
};

export default MapTest;
