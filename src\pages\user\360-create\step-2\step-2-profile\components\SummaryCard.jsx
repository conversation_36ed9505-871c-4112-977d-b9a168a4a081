import { useState } from "react";
import {
  Eye,
  ChatTeardropDots,
  User,
  UserList,
  File,
  Heart,
  Repeat,
  BookmarkSimple,
  CheckCircle,
  UserCircle,
} from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge";
import PropTypes from "prop-types";
import { parseNumber, parseTimeToPersian } from "utils/helper";
import SummaryCardHeaderMenu from "pages/user/hot-topic/components/SummaryCardTopic/SummaryCardHeaderMenu.jsx";
import bookmark from "service/api/bookmark.js";
import { notification } from "utils/helper.js";
import DeletePopUp from "components/ui/DeletePopUp";
import ToolTip from "components/ui/ToolTip";

const SummaryCard = ({
  children,
  media,
  data,
  showMediaName,
  showHeaderMenu = true,
  height,
  handleClickOnAvatar,
  selectable = false,
  handleSelect,
  isSelected,
  showBookMark = false,
  setMyBookmarksLists,
  hasNotes,
  is_pin,
  setUpdater,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [bookmarkToDelete, setBookmarkToDelete] = useState(null);

  const selectMedia = {
    telegram: {
      sub: [
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.view_count || "0")),
          name: "مشاهده‌ها",
        },
        {
          icon: <UserCircle color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.member_count || "0")),
          name: "اعضا",
        },
      ],
    },
    twitter: {
      sub: [
        {
          icon: <Repeat color="#54ADEE" />,
          value: parseNumber(String(data?.copy_count || "0")),
          name: "کپی‌ها",
        },
        {
          icon: <Heart color="#E0526A" weight="fill" />,
          value: parseNumber(String(data?.like_count || "0")),
          name: "لایک‌ها",
        },
      ],
    },
    instagram: {
      sub: [
        {
          icon: <User color="gray" weight="fill" />,
          value: parseNumber(String(data?.following_count || "0")),
          name: "دنبال‌کنندگان",
        },
        {
          icon: <UserList color="gray" weight="fill" />,
          value: parseNumber(String(data?.follower_count || "0")),
          name: "دنبال‌شوندگان",
        },
        {
          icon: <File color="gray" weight="fill" />,
          value: parseNumber(String(data?.post_count || "0")),
          name: "پست‌ها",
        },
      ],
    },
    news: {
      sub: [
        {
          icon: <ChatTeardropDots color="#1CB0A5" weight="fill" />,
          value: parseNumber(String(data?.comment_count || "0")),
          name: "نظرات",
        },
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.view_count || "0")),
          name: "مشاهده‌ها",
        },
      ],
    },
    eitaa: {
      sub: [
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.view_count || "0")),
          name: "مشاهده‌ها",
        },
      ],
    },
  };

  const loadBookmarks = async () => {
    setIsLoading(true);
    try {
      const res = await bookmark.getBookmarks();
      const result = res?.data?.data;
      setMyBookmarksLists(result?.bookmarks);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  const bookmarkHandler = (id) => {
    setBookmarkToDelete(id); // Store the ID of the bookmark to be deleted
    setIsDeletePopUpOpen(true); // Open the delete popup
  };

  const confirmDelete = async () => {
    if (!bookmarkToDelete) return; // Ensure there's a bookmark to delete
    setIsLoading(true);
    try {
      await bookmark.toggleBookmark({
        content_id: bookmarkToDelete,
        content: data,
        platform: media,
      });
      notification.success(
        "محتوای مورد نظر از لیست نشان‌شده‌ها حذف شد.",
        <CheckCircle className="text-light-success-text-rest" size={26} />,
      );
      loadBookmarks(); // Refresh the bookmarks list after deletion
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
    setIsDeletePopUpOpen(false); // Close the delete popup
    setBookmarkToDelete(null); // Clear the stored bookmark ID
  };
  return (
    <div
      className="p-4 flex flex-col justify-between gap-4 min-h-40 w-full [direction:rtl]"
      style={{ ...(height ? { height } : {}) }}
    >
      <div className="flex justify-between">
        <div className="flex gap-4 items-center">
          {selectable && (
            <input
              type="checkbox"
              className="size-6"
              style={{ accentColor: "#343330" }}
              checked={isSelected}
              onChange={(e) => handleSelect(e.target.checked)}
            />
          )}
          {showBookMark && (
            <BookmarkSimple
              color="#6F5CD1"
              size={32}
              weight="fill"
              className="cursor-pointer"
              onClick={() => bookmarkHandler(data?.id)} // Open delete popup on click
            />
          )}
          <div className="flex gap-2 relative">
            <div
              className={"size-10 rounded-full bg-contain ml-2"}
              style={{
                backgroundImage: `url(${
                  data?.avatar || data?.logo_image || "/logo_small.png"
                })`,
                backgroundRepeat: "no-repeat",
                backgroundSize: "contain",
                backgroundPosition: "center center",
                cursor: handleClickOnAvatar ? "zoom-in" : "default",
              }}
              onClick={handleClickOnAvatar}
            ></div>
            {showHeaderMenu && (
              <span
                className={
                  "absolute top-6 right-0 rounded-full overflow-hidden !w-[20px] !h-[20px]"
                }
              >
                <MediaBadge media={media} className={"!h-[20px] !w-[20px]"} />
              </span>
            )}
            <div className="flex flex-col flex-1">
              <span className="font-subtitle-medium text-light-neutral-text-high">
                {data.title || data.full_name || "کاربر"}
              </span>
              <span className="font-overline-medium text-light-neutral-text-medium">
                {media === "news"
                  ? data?.base_url
                  : data?.user_name
                    ? data?.user_name + "@"
                    : ""}
              </span>
            </div>
          </div>
        </div>
        {showHeaderMenu ? (
          <SummaryCardHeaderMenu
            media={media}
            data={data}
            hasNotes={hasNotes}
            is_Pin={is_pin}
            setUpdater={setUpdater}
          />
        ) : (
          <MediaBadge media={media} showMediaName={showMediaName} />
        )}
      </div>
      <div>{children}</div>

      <div className="flex justify-between">
        <div className="flex gap-1 font-overline-medium text-light-neutral-text-medium">
          <span>{data.time ? parseTimeToPersian(data.time) : ""}</span>
        </div>
        <div className="flex gap-4">
          {selectMedia[media]?.sub.map(({ icon, value, name }, index) => (
            <div className="flex items-center gap-1" key={index}>
              <ToolTip comp={name} position="top">
                <div>{icon}</div>
              </ToolTip>
              <span className="font-overline-medium text-light-neutral-text-medium">
                {value}
              </span>
            </div>
          ))}
        </div>
      </div>
      <DeletePopUp
        onClose={() => setIsDeletePopUpOpen(false)}
        isOpen={isDeletePopUpOpen}
        submitHandler={confirmDelete}
        title="آیا می‌خواهید محتوای نشان شده را حذف کنید؟"
      />
    </div>
  );
};

export default SummaryCard;

SummaryCard.propTypes = {
  children: PropTypes.node,
  media: PropTypes.oneOf(["telegram", "twitter", "instagram", "eitaa", "news"])
    .isRequired,
  data: PropTypes.object.isRequired,
  showMediaName: PropTypes.bool,
  showHeaderMenu: PropTypes.bool,
  height: PropTypes.string,
  handleClickOnAvatar: PropTypes.func,
  selectable: PropTypes.bool,
  handleSelect: PropTypes.func,
  isSelected: PropTypes.bool,
  showBookMark: PropTypes.bool,
  setMyBookmarksLists: PropTypes.func,
  hasNotes: PropTypes.bool,
  is_pin: PropTypes.bool,
  setUpdater: PropTypes.func,
};
