import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import NodeCardSelect from "../GraphSettings/components/NodeCardSelect";

const OrgSelect = ({ organNodesData, data, setData }) => {
  const handleSelect = (item) => {
    const exists = data?.some((node) => node.id === item.id);
    setData(
      exists ? data?.filter((node) => node.id !== item.id) : [...data, item]
    );
  };
  const transformedData = Object.keys(organNodesData).map((name, index) => ({
    id: "organization-" + name,
    title: name,
    desc: "هویت فضای مجازی",
    twitter: organNodesData[name].twitter?.toString() || "0",
    telegram: organNodesData[name].telegram?.toString() || "0",
    instagram: organNodesData[name].instagram?.toString() || "0",
    news: organNodesData[name].news?.toString() || "0",
    isSelected: false,
  }));
  return (
    <>
      <p className="mb-2">شخص را جست‌وجو کنید</p>
      <div className="opinion-mining-swiper-container mb-6">
        <Swiper
          spaceBetween={190}
          slidesPerView={4}
          navigation
          pagination={{ clickable: true }}
          modules={[Navigation]}
          loop={true}
          className="swiper-container w-full px-6 kiosk-swiper"
          breakpoints={{
            1280: { slidesPerView: 2.1, spaceBetween: 20 },
          }}
        >
          {transformedData?.map((item) => (
            <SwiperSlide key={item.id}>
              <NodeCardSelect
                title={item?.title}
                twitter={item?.twitter}
                telegram={item?.telegram}
                instagram={item?.instagram}
                news={item?.news}
                handleSelect={() => handleSelect(item)}
                isSelected={data?.some((x) => x.id == item?.id)}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </>
  );
};

export default OrgSelect;
