import React, { useState } from "react";
import SummaryCard from "components/SummaryCard";
import PropTypes from "prop-types";
import TextSlicer from "components/TextSlicer";
import { findPlatform } from "pages/user/hot-topic/utils";
import clsx from "clsx";

const truncateText = (text, maxLength = 110) => {
  if (!text) return "";
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};

const LatestContentCards = ({
  data,
  media,
  selected = false,
  isDropdownOpen,
  platform,
}) => {
  const [select, setSelect] = useState(selected);

  const description =
    media === "news" && data.content
      ? data.content
      : data.description || data?.text || data?.content;
  const truncatedDescription = truncateText(description);

  return (
    <div
      className={clsx(
        "bg-light-neutral-surface-card rounded-lg shadow-[0px_2px_20px_0px_#0000000D] !w-full h-full mb-5",
        select && "outline outline-1 outline-light-primary-border-rest"
      )}
    >
      <SummaryCard
        media={findPlatform(data)}
        data={data}
        platform={platform}
        showMediaName
        // showHeaderMenu={false}
        is360={true}
        handleSelect={setSelect}
        isDropdownOpen={isDropdownOpen}
      >
        <TextSlicer media={media}>
          <p>{truncatedDescription}</p>
        </TextSlicer>
      </SummaryCard>
    </div>
  );
};

export default React.memo(LatestContentCards);

LatestContentCards.propTypes = {
  data: PropTypes.object.isRequired,
  media: PropTypes.string.isRequired,
  platform: PropTypes.string.isRequired,
  selected: PropTypes.bool,
  isDropdownOpen: PropTypes.bool,
};
