import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const BubbleChart = ({ data, setSelectedNode }) => {
  const options = {
    chart: {
      type: "bubble",
      plotBorderWidth: 0,
      borderWidth: 0,
      zooming: {
        enabled: false,
      },
      height: "65%",
    },
    title: {
      text: null,
      style: {
        fontSize: 13,
        fontWeight: "normal",
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ["downloadPNG"],
          x: 0,
          y: -10,
        },
      },
    },
    xAxis: {
      lineWidth: 1,
      lineColor: "#999",
      labels: {
        formatter: function () {
          const max = this.axis.max;
          if (this.value === max) {
            return "";
          }
          return `${toPersianNumber(this.value)}`;
        },
        enabled: true,
        style: {
          fontSize: "12px",
        },
      },
      gridLineWidth: 0,
      tickLength: 5,
      title: {
        text: "فراگیری",
      },
    },
    yAxis: {
      lineWidth: 1,
      lineColor: "#999",
      labels: {
        enabled: true,
        formatter: function () {
          const max = this.axis.max;
          if (this.value === max) {
            return "";
          }
          return `${toPersianNumber((this.value * 100).toFixed(0))}%`;
        },
        style: {
          fontSize: "12px",
        },
      },
      gridLineWidth: 0,
      tickLength: 5,
      title: {
        text: "تاثیرگذاری",
      },
    },
    tooltip: {
      useHTML: true,
      formatter: function () {
        return `
        <div style="font-family: iranyekan; direction:rtl; display:grid; gap:5px;">
          <div style="display: flex; align-items:center; gap:4px;">
            <b>${this.point?.title}</b>
          </div>
          <div style="display: flex; align-items:center; gap:4px;">
            <p>تاثیرگذاری: ${toPersianNumber((this.y * 100).toFixed(0))}%</p>
          </div>
          <div style="display: flex; align-items:center; gap:4px;">
            <p>فراگیری: ${toPersianNumber(this.x)}</p>
          </div>
          <div style="display: flex; align-items:center; gap:4px;">
            <p>تعداد کاربران: ${toPersianNumber(this?.point?.z)}</p>
          </div>
        </div>
        `;
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    series: data,
    plotOptions: {
      bubble: {
        cursor: "pointer",
        point: {
          events: {
            click: function () {
              setSelectedNode(this.title);
            },
          },
        },
      },
    },
  };

  return (
    <div className="w-full">
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default BubbleChart;
