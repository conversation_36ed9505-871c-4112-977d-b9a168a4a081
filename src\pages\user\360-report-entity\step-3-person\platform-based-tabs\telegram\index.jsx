import PropTypes from "prop-types";
import PersonBestSource from "../../components/BestSource";
import LatestContent from "../../components/LatestContent";
// import OwnedResources from "../../components/OwnedResources";
import PersonReleaseProcess from "../../components/ReleaseProcess";
import PersonRepeatHashtags from "../../components/RepeatHashtags";
import PersonRepeatWords from "../../components/RepeatWords";
import ResourcesInfo from "../../components/ResourcesInfo";
import PersonThematicCategory from "../../components/ThematicCategory";
import PersonSentiment from "../../components/Sentiment";
import PersonEntities from "../../components/entities";
import PersonAdvertise from "../../components/advertise";

const TelegramBased = ({ isUpdate }) => {
  return (
    <div className="flex flex-col gap-3 px-4">
      {/* <OwnedResources activeSpan={activeSpan} /> */}
      <ResourcesInfo activePlatform={"telegram"} isUpdate={isUpdate} />
      <LatestContent activePlatform={"telegram"} />
      <div className="flex gap-3 items-stretch">
        <div className="w-3/5">
          <PersonReleaseProcess
            activePlatform={"telegram"}
            isUpdate={isUpdate}
          />
        </div>
        <div className="w-2/5">
          <PersonEntities isUpdate={isUpdate} activePlatform={"telegram"} />
        </div>
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="min-w-0 w-[30%]">
          <PersonSentiment activePlatform={"telegram"} isUpdate={isUpdate} />
        </div>
        <div className="flex-1 min-w-0 w-[70%]">
          <PersonThematicCategory
            activePlatform={"telegram"}
            isUpdate={isUpdate}
          />
        </div>
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="w-3/5">
          <PersonBestSource activePlatform={"telegram"} isUpdate={isUpdate} />
        </div>
        <div className="w-2/5">
          <PersonAdvertise activePlatform={"telegram"} isUpdate={isUpdate} />
        </div>
      </div>
      <div className="flex gap-3 items-end">
        <div className="w-[50%]">
          <PersonRepeatHashtags
            activePlatform={"telegram"}
            isUpdate={isUpdate}
          />
        </div>
        <div className="w-[50%]">
          <PersonRepeatWords activePlatform={"telegram"} isUpdate={isUpdate} />
        </div>
      </div>
    </div>
    // <div className="h-[30rem] w-[90%] flex items-center justify-center font-subtitle-medium">
    //   داده ای برای نمایش وجود ندارد
    // </div>
  );
};

TelegramBased.propTypes = {
  activeSpan: PropTypes.oneOf([
    "telegram",
    "twitter",
    "instagram",
    "news",
    "overview",
  ]).isRequired,
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default TelegramBased;
