import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { fixPercentToShow } from "utils/helper";
import { Card } from "components/ui/Card";
import PropTypes from "prop-types";
import "../../style.css";

const PoliticalChart = ({ showDataLabels }) => {
  const staticSentimentData = {
    "حامیان انقلاب اسلامی": 0.2,
    مستقل: 0.15,
    سلطنت‌طلب: 0.1,
    برانداز: 0.12,
    "فعالین مدنی غرب‌گرا": 0.32,
    منافقین: 0.24,
    احمدی‌نژادی: 0.52,
  };

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay h-full">
      <p className="font-subtitle-large text-right">
        طیف سیاسی (دنبال‌شونده‌ها)
      </p>
      <Divider />
      <div className="flex flex-row justify-between">
        <Doughnut
          showDataLabels={showDataLabels}
          name="sentiment"
          height={240}
          data={Object.entries(staticSentimentData).map(([key, value]) => ({
            name: key,
            y: value,
          }))}
          legendFormatter={function () {
            return `<div dir="rtl" style="font-family: iranyekan; display: grid; grid-template-columns: 1fr 60px; gap: 40px; width: 70%; padding: 2px; align-items: center; margin-right: 1rem;">
            <div style="text-align: center;">
            <span style="font-size: 16px;">
            ${fixPercentToShow(this.y)}
            </span>
            </div>
              <div style="display: flex; gap: 4px; align-items: center; justify-content: flex-end;">
              <span style="color: ${this.color}; font-size: 14px;">
                ${this.name}
              </span>
            </div>
                    </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${
              this.key
            }</div><div>${fixPercentToShow(this.y)}</div></div>`;
          }}
          colors={[
            "#1CB0A5",
            "#E0526A",
            "#00000080",
            "#FFAA00",
            "#008800",
            "#880088",
            "#AAAA00",
          ]}
        />
        <div className="flex flex-col justify-center ml-4"></div>
      </div>
    </Card>
  );
};

PoliticalChart.propTypes = {
  showDataLabels: PropTypes.bool,
};

export default PoliticalChart;
