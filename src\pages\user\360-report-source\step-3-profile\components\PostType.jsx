import { useState, useEffect, useRef } from "react";
import { Heart, Repeat, SpinnerGap } from "@phosphor-icons/react";
import { toPersianNumber } from "utils/helper";
import Reply from "./post-type/reply";
import Quote from "./post-type/quote";
import Repost from "./post-type/repost";
import PropTypes from "prop-types";
import user from "assets/images/360/user.png";
import Mention from "./post-type/mention";

const PostType = ({ influence, quote = [], activatedFilter }) => {
  const [visibleItems, setVisibleItems] = useState(3);
  const [loading, setLoading] = useState(false);
  const loadMoreRef = useRef(null);

  const loadMoreItems = () => {
    setLoading(true);
    setTimeout(() => {
      setVisibleItems((prev) => prev + 3);
      setLoading(false);
    }, 1000);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && !loading && visibleItems < quote.length) {
          loadMoreItems();
        }
      },
      { threshold: 1.0 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loading, visibleItems, quote.length]);

  if (!Array.isArray(quote)) return null;

  const filteredQuotes = activatedFilter
    ? quote.filter((item) => {
        const filterToPostType = {
          mention: "post",
          quote: "qoute",
          repost: "repost",
          reply: "reply",
        };
        return item?.post_type === filterToPostType[activatedFilter];
      })
    : quote;

  return (
    <div className="flex flex-col gap-6">
      {filteredQuotes.slice(0, visibleItems).map((item, index) => {
        const quoteData = {
          title: item?.user_title,
          username: item?.user_name,
          time: item?.date,
          platform: item?.platform,
          avatar: item?.avatar,
          content: item?.text,
          clickedNodeUsername: item?.clickedNodeUsername,
          clickedNodeTitle: item?.clickedNodeTitle,
          centralNodeAvatar: item?.centralNodeAvatar,
          nodeText: item?.quote?.text,
          nodeTime: item?.quote?.date,
          quoteName: item?.quoteName,
          quoteTitle: item?.quoteTitle,
          quoteAvatar: item?.quoteAvatar,
          postUrl: item?.postUrl,
        };

        const selectMedia = {
          twitter: {
            avatar: user,
            sub: [
              {
                icon: <Repeat color="#54ADEE" weight="fill" />,
                value: toPersianNumber(item?.retweet_count || 0),
              },
              {
                icon: <Heart color="#E0526A" weight="fill" />,
                value: toPersianNumber(item?.like_count || 0),
              },
            ],
          },
        };

        const nodeMedia = {
          twitter: {
            avatar: user,
            sub: [
              {
                icon: <Repeat color="#54ADEE" weight="fill" />,
                value: toPersianNumber(item?.quote?.retweet_count || 0),
              },
              {
                icon: <Heart color="#E0526A" weight="fill" />,
                value: toPersianNumber(item?.quote?.like_count || 0),
              },
            ],
          },
        };

        return (
          <div key={index}>
            {item?.post_type === "reply" ? (
              <Reply
                selectMedia={selectMedia}
                staticData={quoteData}
                data={item}
              />
            ) : item?.post_type === "qoute" ? (
              <Quote
                selectMedia={selectMedia}
                nodeMedia={nodeMedia}
                staticData={quoteData}
                data={item}
                influence={influence}
              />
            ) : item?.post_type === "repost" ? (
              <Repost
                selectMedia={selectMedia}
                staticData={quoteData}
                influence={influence}
                data={item}
              />
            ) : item?.post_type === "post" ? (
              <Mention
                selectMedia={selectMedia}
                staticData={quoteData}
                influence={influence}
                data={item}
                type={"منشن"}
              />
            ) : null}
          </div>
        );
      })}
      {loading && (
        <div className="flex justify-center py-4">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      )}
      <div ref={loadMoreRef} className="h-1" />
    </div>
  );
};

PostType.propTypes = {
  quote: PropTypes.array,
  influence: PropTypes.bool,
  activatedFilter: PropTypes.string,
};

export default PostType;
