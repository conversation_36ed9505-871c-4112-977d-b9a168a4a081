import { <PERSON>t<PERSON>eft, UsersThree } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { shortener, toPersianNumber } from "utils/helper.js";
import { useEffect, useState } from "react";
import { Card } from "components/ui/Card.jsx";
import MediaBadge from "components/ui/MediaBadge.jsx";
import CardLoading from "components/ui/CardLoading.jsx";
import Paginate from "components/ui/Paginate.jsx";
import Popup from "../popUp";
import TextWrapper from "../TextWrapper";
import Divider from "components/ui/Divider";
import SentimentAnalysis from "pages/user/hot-topic/components/ShowMoreDetail/SentimentAnalysis";
import TopicCommunication from "pages/user/hot-topic/components/ShowMoreDetail/TopicCommunication";
import ListDrawer from "./ListDrawer";
import fallbackImg from "assets/images/default.png";

const List = ({ items, total, page, handleItemSelect, handlePageChange }) => {
  const [loading, setLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const [showAvatar, setShowAvatar] = useState(false);

  const formatter = new Intl.NumberFormat("en", {
    notation: "compact",
    maximumFractionDigits: 1,
  });

  useEffect(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 300);
  }, [items]);

  return (
    <>
      {loading ? (
        <div className={"flex items-center justify-center min-h-96"}>
          <CardLoading />
        </div>
      ) : items?.data?.length > 0 ? (
        <>
          <div className={"grid grid-cols-5 w-full gap-3 mt-4"}>
            {items?.data?.map((item, index) => (
              <Card
                key={index}
                className={"!py-4 !overflow-hidden cursor-pointer"}
                onClick={() => {
                  setSelectedItem(item);
                  setShowMore(true);
                }}
              >
                <div className={"flex flex-col h-full w-full"}>
                  <div
                    className={
                      "flex flex-col justify-center h-[75%] items-center text-center gap-2"
                    }
                  >
                    <div className="relative">
                      <img
                        src={item.avatar || item.logo_image}
                        alt={item.title}
                        className="w-16 h-16 rounded-full ml-2"
                        onError={(e) => {
                          e.target.onError = null; // Prevents repeated triggers if fallbackImg fails
                          e.target.src = fallbackImg; // Sets the source to the fallback image
                        }}
                      />
                      <span className="absolute top-10 right-0 rounded-full overflow-hidden !w-[25px] !h-[25px]">
                        <MediaBadge
                          media={item.platform}
                          className={"!h-[25px] !w-[25px]"}
                        />
                      </span>
                    </div>
                    <div className="flex flex-col whitespace-nowrap overflow-hidden text-ellipsis">
                      <span
                        className={
                          "text-light-neutral-text-high font-subtitle-large text-ellipsis"
                        }
                      >
                        {shortener(
                          item?.title ||
                            item?.name ||
                            item?.channel_title ||
                            item?.user_title,
                          25
                        )}
                      </span>
                      {item.platform === "news" ? (
                        <div>
                          <span>{item?.url}</span>
                        </div>
                      ) : (
                        <div
                          className={
                            "text-light-neutral-text-medium font-overline-medium"
                          }
                        >
                          <span>
                            {item?.username ||
                              item?.user_name ||
                              item?.channel_username}
                          </span>
                          <span>@</span>
                        </div>
                      )}

                      {item.platform !== "news" && (
                        <div
                          className={
                            "flex justify-center items-center text-light-neutral-text-high font-body-small mt-2"
                          }
                        >
                          <span className={"ml-1"}>
                            {toPersianNumber(
                              formatter.format(
                                item?.follower_count || item?.member_count || 0
                              )
                            )}
                          </span>
                          <UsersThree size={15} color={"#00000080"} />
                        </div>
                      )}
                    </div>
                  </div>
                  <div
                    className="flex justify-center items-center gap-1 text-light-primary-text-rest cursor-pointer border-t border-light-neutral-divider-medium h-[25%] mt-4 pt-4"
                    onClick={() => handleItemSelect(item)}
                  >
                    <div className="flex justify-center items-center gap-1 duration-200 p-2 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]">
                      <span className="font-overline-medium">
                        مشاهده استعلام
                      </span>
                      <CaretLeft size={12} />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          {showMore && (
            <div className="!z-[-999]">
              <Popup
                isOpen={showMore}
                onClose={() => {
                  setShowMore(false);
                  setShowAvatar(false);
                }}
              >
                <div
                  className="!min-w-[30rem] max-w-[40rem]"
                  style={{ ...(showAvatar ? { filter: "blur(4px)" } : {}) }}
                >
                  <div className="flex flex-col gap-4">
                    <ListDrawer
                      data={selectedItem}
                      media={selectedItem?.platform}
                      showMediaName
                      fallbackImg={fallbackImg}
                      showHeaderMenu={false}
                      handleClickOnAvatar={() => setShowAvatar(true)}
                    >
                      <TextWrapper media={selectedItem?.platform}>
                        {selectedItem?.platform === "news" &&
                        selectedItem.content
                          ? selectedItem.content
                          : selectedItem.bio}
                      </TextWrapper>
                    </ListDrawer>
                    <Divider />

                    {selectedItem.sentiment_negative &&
                      selectedItem.sentiment_positive &&
                      selectedItem.sentiment_neutral && (
                        <>
                          <SentimentAnalysis data={selectedItem} />
                          <Divider />
                        </>
                      )}

                    {selectedItem.categories ? (
                      <TopicCommunication data={selectedItem} />
                    ) : (
                      ""
                    )}
                  </div>
                </div>
                {showAvatar && (
                  <div className="h-screen w-full flex justify-center items-center absolute top-0 left-0 z-10">
                    <div
                      className="absolute z-10 w-full h-full top-0 left-0"
                      onClick={() => setShowAvatar(false)}
                    ></div>

                    <div
                      className="size-80 rounded-full mb-[110%] bg-contain cursor-default ring-4 ring-[#432FA7] ring-offset-4 z-20 bg-center bg-no-repeat"
                      style={{
                        backgroundImage: `url(${
                          selectedItem?.avatar ||
                          selectedItem?.logo_image ||
                          fallbackImg
                        })`,
                      }}
                    ></div>
                  </div>
                )}
                <div
                  className="flex justify-center items-center gap-1 text-light-primary-text-rest cursor-pointer mt-5"
                  onClick={() => handleItemSelect(selectedItem)}
                >
                  <div className="flex justify-center items-center gap-1 duration-200 p-1 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]">
                    <span className="font-overline-medium">مشاهده استعلام</span>
                    <CaretLeft size={12} />
                  </div>
                </div>
              </Popup>
            </div>
          )}
          <Paginate
            page={page}
            setPage={handlePageChange}
            dataCount={total}
            per_page={10}
          />
        </>
      ) : (
        <div
          className={
            "flex justify-center items-center text-center min-h-96 font-subtitle-large text-light-neutral-text-high"
          }
        >
          موردی یافت نشد!
        </div>
      )}
    </>
  );
};

List.propTypes = {
  items: PropTypes.array,
  total: PropTypes.number,
  page: PropTypes.number,
  handleItemSelect: PropTypes.func,
  handlePageChange: PropTypes.func,
};

export default List;
