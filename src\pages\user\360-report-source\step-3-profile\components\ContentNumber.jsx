import { memo } from "react";
import { Card } from "components/ui/Card";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import "../../style.css";
import { useEffect, useState } from "react";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData.js";
import <PERSON><PERSON><PERSON> from "./PieChart.jsx";
import { formatShortNumber, toPersianNumber } from "utils/helper.js";
import use360requestStore from "store/360requestStore.js";
import { SpinnerGap } from "@phosphor-icons/react";
import ExportMenu from "components/ExportMenu/index.jsx";

const getPersianLabel = (key, platform) => {
  if (platform === "telegram") {
    const labels = {
      link_count: "لینک",
      file_count: "فایل",
      video_count: "ویدئو",
      photo_count: "عکس",
    };
    return labels[key] || key;
  }
  const labels = {
    post: "توییت",
    repost: "ریتوییت",
    qoute: "کوت",
    reply: "ریپلای",
  };
  return labels[key] || key;
};

const getColorByKey = (key) => {
  const colors = {
    post: "#432FA7",
    repost: "#9A91CE",
    qoute: "gray",
    reply: "black",
    link_count: "#92de54",
    file_count: "#FF5733",
    video_count: "#3357FF",
    photo_count: "#8770ff",
  };
  return colors[key] || "#cccccc";
};

const ContentNumber = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    content_type_distribution:
      state.report?.content?.report_info?.content_type_distribution,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const profile = sourceReport.profile;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Content Type",
      data: data.map(({ count }) => count),
      time: data.map(({ key }) => getPersianLabel(key, sourceReport.platform)),
    },
  ];

  const time = data.map(({ key }) =>
    getPersianLabel(key, sourceReport.platform)
  );

  const getData = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const filters = {
        date,
        platform: sourceReport.platform,
        sources: [sourceId.toString()],
      };

      const requestType =
        sourceReport.platform === "telegram"
          ? "search_in_source"
          : "content_type_distribution";
      const requestData = buildRequestData(filters, requestType);
      const res = await advanceSearch.search(requestData);

      const platformData = {
        twitter: res?.data?.data?.twitter,
        instagram: res?.data?.data?.instagram,
        telegram: res?.data?.data?.telegram,
        eitaa: res?.data?.data?.eitaa,
        news: res?.data?.data?.news,
      };
      const stinasData = platformData[requestData.platform] || [];

      let transformedData = [];
      if (sourceReport.platform === "telegram") {
        const item = stinasData[0] || {};
        transformedData = [
          { key: "link_count", count: item.link_count || 0 },
          { key: "file_count", count: item.file_count || 0 },
          { key: "video_count", count: item.video_count || 0 },
          { key: "photo_count", count: item.photo_count || 0 },
        ].filter((item) => item.count > 0);
      } else {
        transformedData = stinasData.map((item) => ({
          key: item.key,
          count: item.count,
        }));
      }

      updateReportField(
        "content.report_info.content_type_distribution",
        transformedData
      );
      // updatePlatformContent(requestData.platform, requestType, transformedData);

      setData(transformedData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (
      sourceReport.content_type_distribution &&
      Array.isArray(sourceReport.content_type_distribution) &&
      sourceReport.content_type_distribution.length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      setData(sourceReport.content_type_distribution);
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [date]);

  const pieChartData = data.map((item) => ({
    name: getPersianLabel(item.key, sourceReport.platform),
    y: item.count,
    color: getColorByKey(item.key),
    // sliced: item.key === "repost" || item.key === "link_count",
    // selected: item.key === "repost" || item.key === "link_count",
  }));

  return (
    <Card className="card-animation flex-1 card-delay !h-full">
      <div className="flex flex-col gap-2 w-full">
        <div className="flex items-center justify-between">
          <Title title="تعداد محتوای منتشر شده" />
          <ExportMenu
            chartSelector=".content-number-profile-container"
            fileName="content-number"
            series={series}
            time={time}
            excelHeaders={["Content Type", "Count"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            chartTitle="تعداد محتوای منتشر شده"
          />
        </div>
        <div className="content-number-profile-container w-full flex flex-col px-3 items-center justify-center">
          {loading ? (
            <div className="flex w-full h-80 justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : (
            <>
              {pieChartData.length > 0 ? (
                <>
                  <div className="w-[70%] flex justify-center">
                    <PieChart data={pieChartData} />
                  </div>
                  <div className="flex gap-9 justify-center">
                    {data.map((item, i) => (
                      <div key={i} className="flex gap-2 justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: getColorByKey(item.key) }}
                          ></div>
                          {getPersianLabel(item.key, sourceReport.platform)}
                        </div>
                        <div>{`(${toPersianNumber(
                          formatShortNumber(item?.count)
                        )})`}</div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="h-80 flex items-center w-full justify-center font-subtitle-medium">
                  داده ای برای نمایش وجود ندارد
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

export default memo(ContentNumber);
