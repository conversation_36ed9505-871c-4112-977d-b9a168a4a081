import '../../style.css'

const PercentageLine = () => {
    const containerStyle = {
        display: 'flex',
        width: '100%',
        height: '20px',
        overflow: 'hidden',
        borderRadius: '30px'
    };

    const color1Style = {
        width: `${30}%`,
        backgroundColor: '#E0526A',
        height: '100%',
    };

    const color2Style = {
        width: `${100 - 30}%`,
        backgroundColor: '#1CB0A5',
        height: '100%',
    };

    return (
        <div style={containerStyle} className="card-animation card-delay">
            <div style={color2Style}></div>
            <div style={color1Style}></div>
        </div>
    );
};

export default PercentageLine;
