import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import user from "../../../../../assets/images/360/user.png";
import { UsersThree } from "@phosphor-icons/react";
import { toPersianNumber } from "utils/helper";

const OwnedResources = ({ activeSpan }) => {
  const items = new Array(6).fill(null);

  let platformName;
  switch (activeSpan) {
    case "twitter":
      platformName = "اکانت توییتر";
      break;
    case "instagram":
      platformName = "اکانت اینستاگرام";
      break;
    case "telegram":
      platformName = "اکانت تلگرام";
      break;
    default:
      platformName = "خبرگزاری";
  }

  return (
    <Card className="flex flex-col gap-2 !h-full w-full">
      <Title title="منابع مرتبط" />
      <div className="flex gap-8 px-5 h-full pt-3 justify-between">
        {items.map((_, i) => (
          <div key={i} className="flex gap-8">
            <div className="flex items-center">
              <img
                src={user}
                alt="user"
                className="rounded-[50px] w-[50%] h-[50%]"
              />
              <div className="flex flex-col items-center text-center py-3 pt-6">
                <h3 className="font-subtitle-large w-[7rem] pl-2">
                  نام {platformName}
                </h3>
                <span className="font-overline-medium text-[#8f8f8f]">
                  @twitter-ID
                </span>
                <span className="font-body-small flex pt-1 items-center gap-2">
                  <UsersThree color="#8f8f8f" />
                  {toPersianNumber("250k")}
                </span>
              </div>
            </div>
            {i < items.length - 1 && <div className="w-px  bg-gray-200"></div>}
          </div>
        ))}
      </div>
    </Card>
  );
};

OwnedResources.propTypes = {
  activeSpan: PropTypes.string.isRequired,
};

export default OwnedResources;
