import { useState, useEffect, memo } from "react";
import HorizontalBar from "components/Charts/HorizontalBar";
import { Cloud, FunnelSimple, SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import { Tabs360 } from "./360tabs";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";
import { toPersianNumber, preprocessWord } from "utils/helper.js";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const RepeatHashtags = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    frequent_hashtags: state.report?.content?.report_info?.frequent_hashtags,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const profile = sourceReport.profile;
  const platform = sourceReport.platform;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const [categories, setCategories] = useState([]);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("cluster");
  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [info, setInfo] = useState([]);

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Hashtags",
      data: wordCloudData.map(({ value }) => value),
      time: wordCloudData.map(({ text }) => text),
    },
  ];

  const time = wordCloudData.map(({ text }) => text);

  const fetchProfileData = async () => {
    try {
      const filters = {
        date,
        platform: sourceReport.platform,
        sources: [sourceId.toString()],
      };

      const infoQuery = buildRequestData(filters, "cloud", 20);
      const response = await advanceSearch.search(infoQuery, null, {
        cloud_type: "hashtags",
      });

      // Flatten results since we expect a single array of hashtags
      return response.data.data?.[sourceReport.platform]?.flat() || [];
    } catch (error) {
      console.error("Error fetching profile data:", error);
      return [];
    }
  };

  const updateState = (result) => {
    const arabicRange = /[\u0600-\u06FF]/;

    // Process the result to create word cloud data, info, and categories
    const processedData = Array.isArray(result)
      ? result.map(({ key, count }) => {
          let processedKey = preprocessWord(key);
          // Handle hashtag symbol for non-Arabic text
          if (!arabicRange.test(key) && key?.startsWith("#")) {
            processedKey = processedKey.slice(1) + "#";
          }
          return { key: processedKey, count };
        })
      : [];

    // Update wordCloudData
    const newWordCloudData = processedData.map(({ key, count }) => ({
      text: key,
      value: count,
    }));

    // Update info
    const newInfo = processedData.map(({ key, count }) => ({
      word: key,
      count,
    }));

    // Update categories (top 10 hashtags)
    const newCategories = processedData.slice(0, 10).map(({ key }) => key);

    // Avoid unnecessary updates
    if (
      JSON.stringify(newWordCloudData) !== JSON.stringify(wordCloudData) ||
      JSON.stringify(newInfo) !== JSON.stringify(info) ||
      JSON.stringify(newCategories) !== JSON.stringify(categories)
    ) {
      setWordCloudData(newWordCloudData);
      setInfo(newInfo);
      setCategories(newCategories);
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      const result = await fetchProfileData();
      updateReportField("content.report_info.frequent_hashtags", result);
      updateState(result);
    } catch (error) {
      console.error("Error fetching data:", error);
      setWordCloudData([]);
      setInfo([]);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (
      sourceReport.frequent_hashtags &&
      Array.isArray(sourceReport.frequent_hashtags) &&
      sourceReport.frequent_hashtags.length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      updateState(sourceReport.frequent_hashtags);
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [date]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count), 1);
    const minCount = Math.min(...info.map((item) => item.count), 1);

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  return (
    <div className="flex h-full">
      <Card className="px-0 card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="flex items-center w-1/2">
              <Title title="هشتگ‌های پر تکرار" />
              <ExportMenu
                chartSelector=".repeat-hashtags-container"
                fileName="hashtags"
                series={series}
                time={time}
                excelHeaders={["Hashtag", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="هشتگ‌های پر تکرار"
              />
            </div>
            <div className="flex w-1/2 items-center justify-between">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="repeat-hashtags-container grid grid-cols-1 h-full">
            {loading ? (
              <div className="flex w-full h-80 justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : info?.length > 0 ? (
              <>
                {wordCloudActiveTab === "wordCloud" && (
                  <div className="flex flex-1 responsive-svg">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={wordCloudData}
                      callbacks={{
                        getWordTooltip: (word) =>
                          `${word.text} (${toPersianNumber(word.value)})`,
                        onWordClick: handleWordClick,
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && (
                  <HorizontalBar
                    colors={info.map((item) => getGreenColor(item.count))}
                    info={info}
                    categories={categories}
                    loading={loading}
                  />
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent
            word={word}
            source={sourceId.toString()}
            platform={platform}
            date={date}
          />
        </Drawer>
      )}
    </div>
  );
};

export default memo(RepeatHashtags);
