import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { Card } from "components/ui/Card";
import PercentageLine from "./percentageChart";
import "../../style.css";
import { toPersianNumber } from "utils/helper.js";

const ContentDistribution = () => {
  return (
    <Card className="px-0 card-animation card-delay h-[14rem]">
      <div className="w-full flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <Title title="توزیع محتوای تبلیغاتی" className="px-2"></Title>
          <p className="px-2 font-body-small text-[#8f8f8f]">
            در اینجا مشخص می‌شود که چه مقدار از محتوای تولید شده توسط کاربر
            تبلیغاتی هستند و چه مقدار ارزشمند
          </p>
        </div>
        <div className="w-full flex flex-col">
          <div className="flex justify-between px-2">
            <span className="text-green-700">{toPersianNumber("%71")}</span>
            <span className="text-red-500">{toPersianNumber("%29")}</span>
          </div>
          <PercentageLine />
        </div>
      </div>
    </Card>
  );
};

export default ContentDistribution;
