import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper";
import "../../style.css";

const defaultConfig = {
  iconSize: 16,
  buttonMinWidth: 106,
};

export const Tabs360 = ({
  tabArray = [{ id: "1", title: "tab 1", color: "#000" }], // [{id: "uniqueId", title: "عنوان", icon: ICon, color: "#000000"}]
  activeTab = "1",
  onChange = () => {},
  config = defaultConfig,
  className = "",
}) => {
  const mergedConfig = { ...defaultConfig, ...config };

  return (
    <ul
      className={`flex flex-row w-full align-middle py-1 font-body-small text-light-neutral-text-high text-center bg-light-neutral-surface-highlight rounded-[8px] [direction:ltr] ${className}`}
    >
      {tabArray.map(({ id, title, icon: Icon, color, count }) => (
        <li key={id} className="mx-1 flex-1">
          <div
            aria-current="page"
            className={`flex justify-center items-center cursor-pointer
                    w-full h-full px-2 py-1 rounded-[8px]
                    ${
                      activeTab === id
                        ? "bg-light-neutral-surface-card active"
                        : "bg-transparent"
                    }`}
            onClick={() => onChange(id)}
            style={{ minWidth: mergedConfig.buttonMinWidth }}
          >
            <div
              className={"flex text-center gap-1 tab-content"}
              style={{ color: activeTab === id && color }}
            >
              {typeof count === "number" && (
                <span className="tab-title">{`(${toPersianNumber(
                  count,
                )})`}</span>
              )}
              <span className="tab-title">{title}</span>
            </div>
            {Icon && (
              <Icon
                className="inline-block h-full ml-1"
                style={{ color: activeTab === id && color }}
                size={mergedConfig.iconSize}
              />
            )}
          </div>
        </li>
      ))}
    </ul>
  );
};

Tabs360.propTypes = {
  tabArray: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      icon: PropTypes.object,
    }),
  ),
  activeTab: PropTypes.string,
  config: PropTypes.shape({
    iconSize: PropTypes.number,
    buttonMinWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }),
  onChange: PropTypes.func,
  className: PropTypes.string,
};
