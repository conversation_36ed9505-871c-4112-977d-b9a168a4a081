import { useNavigate } from "react-router-dom";
import { useReport360Store } from "store/report360Store.js";
import Step2Profile from "./step-2-profile/index.jsx";
import Step2Location from "./step-2-location/index.jsx";
import Step2Person from "./step-2-person/index.jsx";
import { useEffect } from "react";

const Report360CreateStep2 = () => {
  const { type } = useReport360Store((state) => state.report);
  const navigate = useNavigate();

  useEffect(() => {
    if (!type || !["profile", "location", "topic"].includes(type))
      return navigate("/app/report-360/create/step-1");
  }, []);

  return (
    <div className="flex-1">
      {type === "profile" ? (
        <Step2Profile />
      ) : type === "location" ? (
        <Step2Location />
      ) : type === "topic" ? (
        <Step2Person />
      ) : null}
    </div>
  );
};

export default Report360CreateStep2;
