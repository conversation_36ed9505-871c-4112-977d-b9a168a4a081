import PropTypes from "prop-types";
import { CaretLeft } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import { useReport360Store } from "store/report360Store";
import { useEffect, useState } from "react";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import LatestContentCards from "./LatestContentCards";
import { useNavigate } from "react-router-dom";
import useSearchStore from "store/searchStore";
import Popup from "components/ui/PopUp";
import ExportMenu from "components/ExportMenu/index.jsx";
import use360requestStore from "store/360requestStore";

const LatestContent = ({ activePlatform }) => {
  const [dataArray, setDataArray] = useState([]);
  const [loading, setLoading] = useState(false);
  const { date } = useReport360Store((state) => state.report);
  const { setFilters, setQuery, clearQuery, clearFilter } = useSearchStore();
  const navigate = useNavigate();
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info?.entity,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const entity = sourceReport.entity;

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Latest Content",
      data: dataArray.map((item) => item.text || "No content"), // Assuming 'text' is the content field
      time: dataArray.map((item) => item.created_at || "Unknown"), // Assuming 'created_at' is the timestamp field
    },
  ];

  const time = dataArray.map((item) => item.created_at || "Unknown");

  const getSearchData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          platform: activePlatform,
          date,
          q: entity,
        },
        "advance"
      );

      const responseStinas = await advanceSearch.search(requestData);
      const stinasData = responseStinas?.data?.data;
      const transformedData = stinasData?.[activePlatform] || [];

      setDataArray(transformedData);
    } catch (error) {
      console.error(error);
      setDataArray([]);
    } finally {
      setLoading(false);
    }
  };

  const handleShowMore = async () => {
    try {
      clearFilter();

      setFilters({
        platform: String(activePlatform),
      });
      setFilters({
        date: {
          from: date.from,
          to: date.to,
          index: -1,
        },
      });
      clearQuery();
      setTimeout(() => setQuery(entity), 0);
      navigate(`/app/advanced-search/`);
    } catch (error) {
      console.error("Error in handleShowMore:", error);
    }
  };

  const openConfirmPopup = () => {
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
  };

  const confirmShowMore = () => {
    handleShowMore();
    closeConfirmPopup();
  };

  useEffect(() => {
    getSearchData();
    updateReportField("start_date", date.from);
    updateReportField("end_date", date.to);
  }, [date]);

  return (
    <>
      <Card className="latest-content-container flex flex-col !w-full">
        <div className="w-full flex !justify-between px-3">
          <div className="flex items-center">
            <Title title="جدیدترین محتوا" />
            <ExportMenu
              chartSelector=".latest-content-container"
              fileName="latest-content"
              series={series}
              time={time}
              excelHeaders={["Content", "Created At"]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
            />
          </div>
          <div className="flex items-center gap-4">
            {dataArray?.length !== 0 && (
              <span
                className="flex items-center gap-3 text-[14px] cursor-pointer text-[#6F5CD1]"
                onClick={openConfirmPopup}
              >
                نمایش همه
                <CaretLeft />
              </span>
            )}
          </div>
        </div>

        <div className="flex w-full gap-4">
          {loading ? (
            <div className="w-full flex items-center justify-center font-subtitle-medium py-10">
              در حال بارگذاری...
            </div>
          ) : dataArray?.length !== 0 ? (
            <>
              {dataArray?.slice(0, 4).map((data) => (
                <div
                  key={data.id}
                  className="mt-5 border shadow-md rounded-md bg-white w-full"
                >
                  <LatestContentCards
                    data={data}
                    media={data}
                    platform={activePlatform}
                  />
                </div>
              ))}
            </>
          ) : (
            <div className="w-full flex items-center justify-center font-subtitle-medium py-10">
              داده ای برای نمایش وجود ندارد
            </div>
          )}
        </div>
      </Card>

      <Popup
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={confirmShowMore}
        title="آیا می‌خواهید همه موارد نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه بله به صفحه جستجوی پیشرفته منتقل
          خواهید شد.
        </p>
      </Popup>
    </>
  );
};

LatestContent.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default LatestContent;
