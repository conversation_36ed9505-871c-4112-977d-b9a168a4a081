import { useEffect, useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import { MagnifyingGlass, Warning } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton.jsx";
import { useReport360Store } from "store/report360Store.js";
import DateFilter from "../../step-2-profile/components/SearchBar/DateFilter";
import PropTypes from "prop-types";
import use360requestStore from "store/360requestStore";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { notification } from "utils/helper";

const LocationSearchBar = ({ clickedCityName, nextStep, loading = false }) => {
  const { type, date } = useReport360Store((state) => state.report);
  const setReport = useReport360Store((state) => state.setReport);

  const [searchValue, setSearchValue] = useState();
  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setReport({ date: { from: from, to: to } });
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      await handleSubmit();
    }
  };
  const breadcrumbList = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "استعلام مکان محور" },
  ];
  useBreadcrumb(breadcrumbList);
  const handleSubmit = async () => {
    if (!searchValue) {
      notification.warning(
        `لطفا مکان مورد نظر را از روی نقشه پیدا کرده و یا آن را جستجو کنید`,
        <Warning size={25} className="text-light-warning-text-rest" />
      );
      return;
    }
    // await updateReport("q", searchValue);
    // await setReport({ entity: searchValue || clickedCityName });
    nextStep(searchValue);
    // await search(searchValue);
  };

  useEffect(() => {
    setSearchValue(clickedCityName);
  }, [clickedCityName]);

  return (
    <div className="font-body-medium mx-5 h-full bg-white p-5 overflow-hidden rounded-lg shadow-md z-[-1]">
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"lg"}
          validation={"none"}
          value={clickedCityName}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={"نام شهر را جست‌و‌جو کنید"}
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={handleSubmit}
          size={"lg"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          استعلام
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        <DateFilter
          type={type}
          handleDateChange={handleDateChange}
          selectedDateRange={date}
        />

        <div>
          <span className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2">
            نوع استعلام:
            <span className="text-[#7f7f7f]">مکان</span>
            {/*<CaretLeft />*/}
          </span>
        </div>
      </div>
    </div>
  );
};

LocationSearchBar.propTypes = {
  search: PropTypes.func.isRequired,
  nextStep: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  clickedCityName: PropTypes.string,
};

export default LocationSearchBar;
