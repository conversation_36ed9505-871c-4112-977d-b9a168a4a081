import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import { shortener, toPersianNumber } from "utils/helper.js";

const MultipleBar = ({ categories = [], data = [], groupPadding = 0.2 }) => {
  const [num, setNum] = useState(0);

  const isSingleColumn = data?.length === 1 && data[0]?.data?.length === 1;

  const chartOptions = {
    chart: {
      type: "column",
    },
    title: {
      text: null,
    },
    legend: {
      enabled: false,
      align: "right",
      useHTML: true,
      labelFormatter: function () {
        return `<div style="font-family:'iranyekan',serif;font-size: 13px; direction: rtl;">
            ${shortener(this.name, 12)}
          </div>`;
      },
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      categories,
      crosshair: true,
      accessibility: {
        description: "categories",
      },
      labels: {
        style: {
          fontWeight: "600",
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      style: {
        fontFamily: "IranYekan",
      },
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl;">`;
        this.points.forEach((point) => {
          tooltipHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <strong style="color: #333;">${toPersianNumber(
                point.y
              )}%</strong>
            </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    plotOptions: {
      column: {
        pointPadding: 0,
        groupPadding: isSingleColumn ? 0.1 : groupPadding,
        borderWidth: 0,
        pointWidth: isSingleColumn ? 60 : undefined,
      },
      series: {
        enableMouseTracking: true,
        borderRadius: {
          radius: 8,
        },
      },
    },
    series: data,
  };

  useEffect(() => {
    setNum((l) => l + 1);
  }, [data]);

  return (
    <div className="w-full !h-full">
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        key={num}
      />
    </div>
  );
};

MultipleBar.propTypes = {
  categories: PropTypes.array.isRequired,
  data: PropTypes.array.isRequired,
  groupPadding: PropTypes.number,
};

export default MultipleBar;
