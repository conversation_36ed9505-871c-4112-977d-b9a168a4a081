import Divider from "components/ui/Divider";

const OpinionMiningGuide = () => {
  return (
    <div className="font-body-medium">
      <p className="font-subtitle-medium">شناسایی روابط چیست؟</p>

      <p className="text-justify pb-4">
        در این بخش، گره‌های یک گراف شبکه‌ای توسط کاربر تعریف می‌شوند و سامانه به
        صورت خودکار پیوند میان این گره‌ها را تا چند سطح پردازش کرده و نمایش
        می‌دهد.
      </p>
      <Divider className="py-2" />

      <p className="font-subtitle-medium">گره‌ها</p>

      <p>هر گره به نوعی یک موجودیت است که می‌تواند شامل موارد زیر باشد:</p>

      <ul className="list-disc p-4">
        <li>پروفایل کاربری یا منبع</li>
        <li>اسامی اشخاص</li>
        <li>اسامی سازمان‌ها</li>
        <li>اسامی مکان‌ها</li>
      </ul>

      <Divider className="py-2" />

      <p className="font-subtitle-medium">یال‌ها</p>

      <p>
        ارتباطات بین گره‌ها (هر گره با سایر گره‌ها) از طریق موارد زیر شکل خواهد
        گرفت که قابلیت سفارشی‌سازی دارد:
      </p>

      <ul className="list-disc p-4">
        <li className="font-subtitle-medium py-1">ارتباط مستقیم:</li>
        <li>منشن شدن یک منبع در محتوای منبع دیگر</li>
        <li>ریتوییت شدن محتوای یک منبع توسط منبع دیگر</li>
        <li>داشتن کامنت بر روی محتوای تولید شده یک منبع توسط منبع دیگر</li>
        <li>کوت شدن محتوای یک منبع توسط سایر منابع</li>
      </ul>

      <ul className="list-disc p-4">
        <li className="font-subtitle-medium py-1">ارتباط غیرمستقیم:</li>
        <li>ارتباط بین دو گره از طریق یک گره واسط شکل بگیرد</li>
        <li>طیف سیاسی مشترک</li>
        <li>فضای فکری مشترک (مشترک بودن لیست علاقمندی‌ها)</li>
      </ul>
    </div>
  );
};

export default OpinionMiningGuide;
