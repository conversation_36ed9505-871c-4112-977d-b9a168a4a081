import { useState, useEffect } from "react";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import { preprocessWord, toPersianNumber } from "utils/helper";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import DropDown from "components/ui/DropDown";

const PersonFrequentEntities = () => {
  const [loading, setLoading] = useState(false);
  const { date } = useReport360Store((state) => state.report);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [activeTab, setActiveTab] = useState("سازمان ها");

  const fetchData = async () => {
    setLoading(true);
    const simulatedResponse = [
      { word: "نمونه", count: 10 },
      { word: "کلمه", count: 5 },
      { word: "استاتیک", count: 8 },
      { word: "آزمایش", count: 12 },
      { word: "ری‌اکت", count: 7 },
      { word: "جاوااسکریپت", count: 9 },
      { word: "ابری", count: 6 },
      { word: "داده", count: 11 },
      { word: "تصویری‌سازی", count: 4 },
      { word: "کامپوننت", count: 3 },
    ];

    const processedWords = simulatedResponse.map(({ word, count }) => ({
      text: preprocessWord(word),
      value: count,
    }));
    setWordCloudData(processedWords);
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [date]);

  const tabs = [
    { id: "wordCloud", title: "رویداد" },
    { id: "cluster", title: "مکان ها" },
    { id: "organization", title: "سازمان ها" },
    { id: "person", title: "اشخاص" },
  ];

  if (loading) {
    return (
      <div className="flex">
        <Card className={`flex flex-col gap-4 w-full`}>
          <div className="flex w-full h-80 justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex h-full !w-full">
      <Card className="px-0 card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row items-center gap-10 w-full justify-between">
            <Title title="موجودیت های پرتکرار"></Title>

            {/* Show tabs for larger screens */}
            <div className="tabs-360 gap-3 px-2 font-overline-medium">
              {tabs.map((tab, i) => (
                <span
                  key={i}
                  onClick={() => setActiveTab(tab.title)}
                  className={`duration-200 cursor-pointer p-2 rounded ${
                    activeTab === tab.title
                      ? "text-[#6F5CD1] bg-[#E9E6F7]"
                      : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
                  }`}
                >
                  {tab.title}
                </span>
              ))}
            </div>

            {/* Show dropdown for smaller screens */}
            <div className="dropdown-360">
              <DropDown
                title="انتخاب تب"
                subsets={tabs.map((tab) => tab.title)}
                selected={activeTab}
                setSelected={(value) => setActiveTab(value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1">
            <div className="flex flex-1 responsive-svg">
              <ReactWordcloud
                options={{
                  rotations: 1,
                  rotationAngles: [0],
                  enableTooltip: true,
                  deterministic: false,
                  fontFamily: "iranyekan",
                  fontSizes: [14, 54],
                  padding: 10,
                  colors: CLUSTER_COLORS,
                  tooltipOptions: { theme: "light", arrow: true },
                }}
                words={wordCloudData}
                callbacks={{
                  getWordTooltip: (word) => {
                    const countInPersian = toPersianNumber(word?.value);
                    return `${word.text} (${countInPersian})`;
                  },
                }}
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PersonFrequentEntities;
