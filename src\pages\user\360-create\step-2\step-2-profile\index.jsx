import { useEffect, useState } from "react";
import advanceSearch from "service/api/advanceSearch.js";
import SearchBar from "./components/SearchBar/index.jsx";
import ProfileList from "./components/ProfileList/index.jsx";
import PLATFORMS from "constants/platforms.js";
import { buildRequestData } from "utils/requestData.js";
import { useReport360Store } from "store/report360Store.js";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context.jsx";
import { useNavigate } from "react-router-dom";

const Step2Profile = () => {
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [page, setPage] = useState(1);
  const [forceReFetch, setForceReFetch] = useState(0);
  const [activeTab, setActiveTab] = useState("all");
  const [searchValue, setSearchValue] = useState("");
  const [queryValue, setQueryValue] = useState("");
  const [totals, setTotals] = useState({
    all: 0,
    twitter: 0,
    news: 0,
    telegram: 0,
    instagram: 0,
  });
  const { date } = useReport360Store((state) => state.report);

  const search = async (
    query,
    page = 1,
    pageSize = 10,
    onlyResetAll = false
  ) => {
    if (loading || !query || query === "") return;
    setLoading(true);
    setQueryValue(query);
    try {
      setSearchValue(query);
      const reqsQueries = [
        PLATFORMS.TWITTER,
        PLATFORMS.NEWS,
        PLATFORMS.TELEGRAM,
        PLATFORMS.INSTAGRAM,
      ].map((platform) => {
        const filters = {
          date,
          platform: platform,
          q: query,
          page,
        };
        const infoQuery = buildRequestData(filters, "search_in_source");
        return advanceSearch.search(infoQuery);
      });

      const response = await Promise.allSettled(reqsQueries);
      const result = response
        // .filter((res) => res.status === "fulfilled")
        .map((res, index) => {
          try {
            if (res.status !== "fulfilled")
              return {
                data: [],
                meta: {},
              };
            if (typeof res?.value?.data?.data !== "string") {
              const data = res.value.data.data;

              setTotals((prev) => ({
                ...prev,
                [index === 0 ? "twitter" : index === 1 ? "news" : "telegram"]:
                  data?.total || 0,
              }));

              return {
                data: data.twitter || data.news || data.telegram || [],
              };
            } else {
              const data = JSON.parse(res?.value?.data?.data ?? "");
              return {
                data: data.result.data || [],
                meta: data.result.meta || {},
              };
            }
          } catch (error) {
            console.log(error);
            return {
              data: [],
              meta: {},
            };
          }
        });

      result[0].data =
        result[0]?.data?.map((item) => ({
          ...item,
          platform: PLATFORMS.TWITTER,
        })) || [];
      result[1].data =
        result[1]?.data?.map((item) => ({
          ...item,
          platform: PLATFORMS.NEWS,
        })) || [];
      result[2].data =
        result[2]?.data?.map((item) => ({
          ...item,
          platform: PLATFORMS.TELEGRAM,
        })) || [];
      result[3].data =
        result[3]?.data?.map((item) => ({
          ...item,
          platform: PLATFORMS.TELEGRAM,
        })) || [];

      const allItems = {
        data: [
          ...(result?.[0]?.data || []),
          ...(result?.[1]?.data || []),
          ...(result?.[2]?.data || []),
          ...(result?.[3]?.data || []),
        ].sort((a, b) => {
          if (a.influence === null || a.influence === undefined) return 1;
          if (b.influence === null || b.influence === undefined) return -1;
          return b.influence - a.influence;
        }),
        meta: {
          total: Math.max(
            totals.twitter,
            totals.news,
            totals.telegram,
            totals.instagram
          ),
          page: onlyResetAll ? page : 1,
        },
      };

      setTotals((prev) => ({
        ...prev,
        all: allItems.meta.total,
      }));

      if (onlyResetAll) {
        setItems({
          ...items,
          all: allItems,
        });
      } else {
        setItems({
          all: allItems,
          twitter: result[0],
          news: result[1],
          telegram: result[2],
          instagram: result[3],
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const { setBreadcrumb } = useLayoutContext();
  const breadcrumbList = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "استعلام منبع", link: "/app/report-360/create" },
    { title: queryValue || "جست‌وجو" },
  ];
  useBreadcrumb(breadcrumbList);

  useEffect(() => {
    if (searchValue && searchValue.trim() !== "") {
      if (activeTab === "all") search(searchValue, page, 10, true);
      else search(searchValue, page, 10);
    }
  }, [page, forceReFetch, activeTab]);

  useEffect(() => {
    setBreadcrumb(breadcrumbList);
  }, [queryValue]);

  const handlePageChange = (newPage) => {
    setForceReFetch((prev) => prev + 1);
    setPage(newPage);
  };

  const nextStep = (platform, id) => {
    navigate(`/app/report-360/report/source/${platform}/${id}`);
  };

  return (
    <div className="h-full w-full pt-6 [direction:rtl]">
      <SearchBar search={search} loading={loading} setLoading={setLoading} />
      <ProfileList
        items={items}
        loading={loading}
        handlePageChange={handlePageChange}
        total={totals[activeTab] || 0}
        page={page}
        setPage={setPage}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        nextStep={nextStep}
      />
    </div>
  );
};

export default Step2Profile;
