import { memo, useState } from "react";
import HorizontalBar from "components/Charts/HorizontalBar";
import { Cloud, FunnelSimple } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import PropTypes from "prop-types";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { Tabs360 } from "./360tabs";
import { preprocessWord, toPersianNumber } from "utils/helper";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const PersonRepeatHashtags = ({ activePlatform }) => {
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("wordCloud");
  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");

  const sourceReport = use360requestStore((state) => ({
    hashtags: state.report?.content?.report_info?.hashtags,
  }));

  const data = sourceReport.hashtags?.[activePlatform] || [];

  const words = data.map(({ key, count }) => ({
    text: preprocessWord(key),
    value: count,
  }));

  const info = data.map(({ key, count }) => ({
    word: preprocessWord(key),
    count,
  }));
  const categories = data.map(({ key }) => preprocessWord(key));

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Hashtag Count",
      data: words.map((item) => item.value),
      time: words.map((item) => item.text),
    },
  ];

  const time = words.map((item) => item.text);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  return (
    <div className="flex">
      <Card className="px-0 card-animation card-delay !h-[31rem]">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="w-1/2 flex items-center">
              <Title title="هشتگ‌های پر تکرار" />
              <ExportMenu
                chartSelector=".person-repeat-hashtags-container"
                fileName="person-repeat-hashtags"
                series={series}
                time={time}
                excelHeaders={["Hashtag", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="هشتگ‌های پر تکرار"
              />
            </div>
            <div className="flex items-center gap-4 w-1/2">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="grid person-repeat-hashtags-container grid-cols-1 h-full">
            {info?.length ? (
              <>
                {wordCloudActiveTab === "wordCloud" && (
                  <div dir="ltr" className="flex flex-1 responsive-svg h-full">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={words}
                      callbacks={{
                        getWordTooltip: (word) => {
                          const countInPersian = toPersianNumber(word.value);
                          return `${word.text} (${countInPersian})`;
                        },
                        onWordClick: handleWordClick,
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && (
                  <div className="flex justify-end h-full w-full">
                    <HorizontalBar
                      colors={info.map((item) => getGreenColor(item.count))}
                      info={info}
                      categories={categories}
                      style={{ height: "100%" }}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent word={word} />
        </Drawer>
      )}
    </div>
  );
};

PersonRepeatHashtags.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default memo(PersonRepeatHashtags);
