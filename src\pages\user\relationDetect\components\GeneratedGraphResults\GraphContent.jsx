import { useRef, useEffect, useState, memo } from "react";
import ForceGraph2D from "react-force-graph-2d";
import * as d3 from "d3";
import { forceCollide } from "d3-force-3d";
import {
  MapPin,
  Repeat,
  PencilSimpleLine,
  At,
  ChatCircle,
} from "@phosphor-icons/react";
import defaultNodeImage from "assets/images/default.png";
import NodeDrawer from "pages/user/360-report-source/step-3-profile/components/NodeDrawer";
import { notification, shortener } from "utils/helper";
import { useRelationStore } from "store/relationDetectStore";
import advanceSearch from "service/api/advanceSearch";
import { useLocation } from "react-router-dom";
import colorMap from "../GraphSettings/constants/graphColorsMap";

const GraphContent = ({ finalGraphData }) => {
  const location = useLocation();
  const [myData, setMyData] = useState(
    location?.state?.finalGraphData || finalGraphData || null
  );
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);
  const [postCounts, setPostCounts] = useState({
    repost: 0,
    quote: 0,
    mention: 0,
    reply: 0,
  });
  const [quote, setQuote] = useState([]);
  const [nodeInfo, setNodeInfo] = useState(null);
  const [totalCounts, setTotalCounts] = useState(0);
  const [showMore, setShowMore] = useState(false);
  const [hoveredNode, setHoveredNode] = useState(null);
  const fgRef = useRef();
  const [dimensions, setDimensions] = useState({ width: 800, height: 670 });
  const containerRef = useRef();
  const imageCache = useRef({});
  const [graphData, setGraphData] = useState({
    nodes: [],
    links: [],
    node_links_count: {},
  });

  // Update myData when finalGraphData or location.state changes
  useEffect(() => {
    setMyData(location?.state?.finalGraphData || finalGraphData || null);
  }, [finalGraphData, location?.state?.finalGraphData]);

  // Preload defaultNodeImage
  useEffect(() => {
    const defaultImg = new Image();
    imageCache.current["default"] = {
      img: defaultImg,
      status: "loading",
    };
    defaultImg.onload = () => {
      imageCache.current["default"] = {
        img: defaultImg,
        status: "loaded",
      };
    };
    defaultImg.onerror = () => {
      console.warn(`Failed to load default image: ${defaultNodeImage}`);
      imageCache.current["default"] = {
        img: null,
        status: "failed",
      };
    };
    defaultImg.src = defaultNodeImage;
  }, []);

  // Process myData for graph rendering
  useEffect(() => {
    if (!myData || !myData?.nodes || !myData?.links) {
      console.warn("Invalid or missing graph data:", myData);
      setGraphData({ nodes: [], links: [], node_links_count: {} });
      return;
    }

    try {
      const nodes = Object.entries(myData.nodes).map(([node_name, data]) => ({
        node_name,
        node_title:
          data?.entity_title || data?.user_title || node_name || "Unknown",
        node_avatar: data?.avatar || null,
        node_type: data?.entity_title ? "entity" : "user",
        follower_count: data?.follower_count || 0,
        level: data?.follower_count ? Math.log10(data.follower_count + 1) : 3,
        path: shortener(
          data?.entity_title || data?.user_title || node_name || "Unknown",
          15,
          "rtl"
        ),
        ...data,
      }));

      const links = [];
      myData.links.forEach((link) => {
        const relation = link.relation || {};
        const relationTypes = Object.keys(relation).filter((key) =>
          ["quote", "retweet", "reply", "mention", "connection"].includes(key)
        );

        const sourceId =
          typeof link.source === "string"
            ? link.source
            : link.source?.node_name;
        const targetId =
          typeof link.target === "string"
            ? link.target
            : link.target?.node_name;

        if (!sourceId || !targetId) {
          console.warn("Invalid link, missing source or target:", link);
          return;
        }

        if (relationTypes.length === 0) {
          links.push({
            source: sourceId,
            target: targetId,
            type: "connection",
            relation_count: relation.connection || 1,
          });
        } else {
          relationTypes.forEach((type) => {
            if (relation[type] > 0) {
              links.push({
                source: sourceId,
                target: targetId,
                type,
                relation_count: relation[type] || 1,
              });
            }
          });
        }
      });

      const node_links_count = links.reduce((acc, link) => {
        acc[link.source] = (acc[link.source] || 0) + (link.relation_count || 1);
        acc[link.target] = (acc[link.target] || 0) + (link.relation_count || 1);
        return acc;
      }, {});

      setGraphData({
        nodes,
        links,
        node_links_count,
      });
    } catch (error) {
      console.error("Error processing graph data:", error);
      setGraphData({ nodes: [], links: [], node_links_count: {} });
    }
  }, [myData]);

  // Preload images for user nodes
  useEffect(() => {
    graphData.nodes.forEach((node) => {
      if (node.node_type === "user" && node.node_name) {
        const cached = imageCache.current[node.node_name];
        if (cached && cached.status !== "loading") return;

        const imgSrc =
          node.node_avatar &&
          node.node_avatar !==
            "https://s3.synappse.ir/twitter/profiles/None.jpg" &&
          node.node_avatar !== "null" &&
          node.node_avatar !== "undefined"
            ? node.node_avatar
            : defaultNodeImage;

        const img = new Image();
        imageCache.current[node.node_name] = {
          img,
          status: "loading",
        };
        img.onload = () => {
          imageCache.current[node.node_name] = {
            img,
            status: "loaded",
          };
        };
        img.onerror = () => {
          console.warn(
            `Failed to load image for node ${node.node_name}: ${imgSrc}`
          );
          imageCache.current[node.node_name] = {
            img: imageCache.current["default"]?.img || null,
            status: imageCache.current["default"]?.status || "failed",
          };
        };
        img.src = imgSrc;
      }
    });
  }, [graphData.nodes]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        try {
          const { width, height } =
            containerRef.current.getBoundingClientRect();
          setDimensions({ width, height });
        } catch (error) {
          console.error("Error getting container dimensions:", error);
        }
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Configure D3 forces
  useEffect(() => {
    const graph = fgRef.current;
    if (graph) {
      graph.d3Force("charge", d3.forceManyBody().strength(-8));
      graph.d3Force("link").distance(120);
      graph.d3Force("center").strength(0.1);
      graph.d3Force(
        "collision",
        forceCollide((node) => Math.sqrt((node.level || 3) * 20))
      );
    }
  }, []);

  // Handle link click
  const handleLinkClick = async (link) => {
    const sourceId =
      typeof link.source === "string" ? link.source : link.source?.node_name;
    const targetId =
      typeof link.target === "string" ? link.target : link.target?.node_name;

    const payload = {
      start_date: edgeDetails?.timeSettings?.from,
      end_date: edgeDetails?.timeSettings?.to,
      order: "asc",
      page: 1,
      platform: "twitter",
      q: targetId,
      report_type: "related_content",
      rows: 20,
      sort: "date",
      sources: [sourceId],
    };

    try {
      const response = await advanceSearch.search(payload);
      const responseDataArray = response?.data?.data?.twitter || [];
      const sourceNode = graphData.nodes.find(
        (node) => node.node_name === sourceId
      );
      const targetNode = graphData.nodes.find(
        (node) => node.node_name === targetId
      );
      const combinedDataArray = responseDataArray.map((responseData) => ({
        avatar:
          responseData?.avatar || targetNode?.node_avatar || defaultNodeImage,
        originalAvatar:
          responseData?.original_avatar || targetNode?.original_avatar || null,
        followerCount:
          responseData?.follower_count || targetNode?.follower_count || 0,
        userName: responseData?.user_name || targetNode?.node_name || null,
        userTitle: responseData?.user_title || targetNode?.node_title || null,
        quote: responseData?.quote || null,
        postType: responseData?.post_type || null,
        text: responseData?.text || null,
        centralNodeAvatar: targetNode?.node_avatar || defaultNodeImage,
        centralNodeTitle: targetNode?.node_title || null,
        centralNodeUsername: targetNode?.node_name || null,
        centralNodeFollower: targetNode?.follower_count || 0,
        quoteName: responseData?.quote?.user_name || null,
        quoteTitle: responseData?.quote?.user_title || null,
        quoteAvatar: responseData?.quote?.avatar || null,
        nodeCount: responseDataArray.length,
        totalCount: responseDataArray.length,
      }));
      setNodeInfo(combinedDataArray);
      setShowMore(true);
      const counts = {
        repost: responseDataArray.filter(
          (item) => item.post_type === "retweet" || item.post_type === "repost"
        ).length,
        quote: responseDataArray.filter(
          (item) => item.post_type === "quote" || item.post_type === "qoute"
        ).length,
        mention: responseDataArray.filter(
          (item) => item.post_type === "mention" || item.post_type === "post"
        ).length,
        reply: responseDataArray.filter((item) => item.post_type === "reply")
          .length,
      };
      setPostCounts(counts);
      setTotalCounts(responseDataArray.length);
      setQuote(
        responseDataArray.map((responseData) => ({
          ...responseData,
          avatar: responseData?.avatar || defaultNodeImage,
          centralNodeAvatar: sourceNode?.node_avatar || defaultNodeImage,
          clickedNodeTitle:
            targetNode?.node_title || responseData?.user_title || null,
          clickedNodeUsername:
            targetNode?.node_name || responseData?.user_name || null,
          quoteName: responseData?.quote?.user_name || null,
          quoteTitle: responseData?.quote?.user_title || null,
          quoteAvatar: responseData?.quote?.avatar || null,
          postUrl: responseData?.post_url || null,
          platform: "twitter",
        }))
      );
    } catch (error) {
      notification.error(
        error?.response?.data?.message || "خطا در دریافت محتوای مرتبط",
        <MapPin size={20} className="text-light-error-background-rest" />
      );
    }
  };

  const renderNode = (node, ctx, globalScale) => {
    const baseSize = 15;
    const scaleFactor = 3;
    const imgSize = baseSize + (node.level || 3) * scaleFactor;
    const borderWidth = 1;
    const borderColor = "#9198AD";
    const padding = 1; // Match GraphPreview

    const defaultCached = imageCache.current["default"];
    const cached = imageCache.current[node.node_name];

    const useDefaultImage =
      node.node_type === "entity" ||
      !cached ||
      cached.status !== "loaded" ||
      !cached.img ||
      node.node_avatar === "https://s3.synappse.ir/twitter/profiles/None.jpg" ||
      node.node_avatar === "null" ||
      node.node_avatar === "undefined";

    if (
      useDefaultImage &&
      defaultCached?.status === "loaded" &&
      defaultCached.img
    ) {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.drawImage(
        defaultCached.img,
        node.x - imgSize / 2,
        node.y - imgSize / 2,
        imgSize,
        imgSize
      );
      ctx.restore();
    } else if (!useDefaultImage) {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.drawImage(
        cached.img,
        node.x - imgSize / 2,
        node.y - imgSize / 2,
        imgSize,
        imgSize
      );
      ctx.restore();
    } else {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.fillStyle = "#CCCCCC";
      ctx.fill();
      ctx.restore();
    }

    ctx.beginPath();
    ctx.arc(
      node.x,
      node.y,
      imgSize / 2 + padding + borderWidth / 2,
      0,
      Math.PI * 2
    );
    ctx.lineWidth = borderWidth;
    ctx.strokeStyle = borderColor;
    ctx.stroke();
    ctx.closePath();

    // Render node name below the node
    if (globalScale > 0.5 && node.path) {
      ctx.font = `${12 / globalScale}px 'iranyekan', Arial`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "black";
      ctx.fillText(node.path, node.x, node.y + imgSize / 2 + 10);
    }
  };

  const nodePointerAreaPaint = (node, color, ctx) => {
    const baseSize = 15;
    const scaleFactor = 3;
    const imgSize = baseSize + (node.level || 3) * scaleFactor;
    const padding = 1;
    const borderWidth = 1;
    const hitboxRadius = imgSize / 2 + padding + borderWidth + 5;

    ctx.beginPath();
    ctx.arc(node.x, node.y, hitboxRadius, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
  };

  const renderLink = (link, ctx, globalScale) => {
    const sourceId =
      typeof link.source === "string" ? link.source : link.source?.node_name;
    const targetId =
      typeof link.target === "string" ? link.target : link.target?.node_name;

    const sourceCount = graphData.node_links_count[sourceId] || 0;
    const targetCount = graphData.node_links_count[targetId] || 0;
    const linkColor =
      sourceCount > 1 || targetCount > 1
        ? "#000000"
        : colorMap[link.type] || "#9198AD";

    const nodeRadius = (15 + (link.source.level || 3) * 3) / 2 / globalScale;
    const padding = 1 / globalScale;
    const borderWidth = 1 / globalScale;
    const nodeOffset = nodeRadius + padding + borderWidth / 2;

    const dx = link.target.x - link.source.x;
    const dy = link.target.y - link.source.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) return;

    const unitDx = dx / length;
    const unitDy = dy / length;

    const startX = link.source.x + unitDx * nodeOffset;
    const startY = link.source.y + unitDy * nodeOffset;
    const endX = link.target.x - unitDx * nodeOffset;
    const endY = link.target.y - unitDy * nodeOffset;

    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = linkColor;
    ctx.lineWidth = 0.8 / globalScale;
    ctx.stroke();

    if (link.source !== link.target) {
      const arrowLength = 5;
      const arrowWidth = 5;
      const t = 0.9;
      const x = startX + (endX - startX) * t;
      const y = startY + (endY - startY) * t;
      const angle = Math.atan2(endY - startY, endX - startX);

      ctx.save();
      ctx.beginPath();
      ctx.translate(x, y);
      ctx.rotate(angle);
      ctx.moveTo(0, 0);
      ctx.lineTo(-arrowLength, -arrowWidth / 2);
      ctx.lineTo(-arrowLength, arrowWidth / 2);
      ctx.closePath();
      ctx.fillStyle = linkColor;
      ctx.fill();
      ctx.restore();
    }
  };

  const post_type_badges = [
    {
      name: "ریتوئیت",
      engName: "repost",
      icon: <Repeat color="#FF9408" />,
      count: postCounts.repost,
      bgColor: "#f9dbb2",
    },
    {
      name: "کوت",
      engName: "quote",
      icon: <PencilSimpleLine color="#DB6DE5" />,
      count: postCounts.quote,
      bgColor: "#efd0f5",
    },
    {
      name: "ریپلای",
      engName: "reply",
      icon: <ChatCircle color="#1DCEA3" />,
      count: postCounts.reply,
      bgColor: "#b6ede1",
    },
    {
      name: "منشن",
      engName: "mention",
      icon: <At color="#6D72E5" />,
      count: postCounts.mention,
      bgColor: "#d0d1f5",
    },
  ];
  return (
    <>
      <div className="h-[670px]" ref={containerRef}>
        <ForceGraph2D
          ref={fgRef}
          graphData={graphData}
          dagMode={null}
          dagLevelDistance={300}
          backgroundColor="transparent"
          nodeRelSize={1}
          nodeId="node_name"
          nodeVal={(node) => (node.level || 3) * 10}
          nodeLabel={(node) => (hoveredNode === node ? node.node_title : "")}
          linkDirectionalParticles={2}
          linkDirectionalParticleWidth={0}
          d3VelocityDecay={0.3}
          width={dimensions.width}
          height={dimensions.height}
          zoom={0.8}
          maxZoom={10}
          minZoom={0.4}
          nodeCanvasObject={renderNode}
          nodeCanvasObjectMode={() => "replace"}
          nodePointerAreaPaint={nodePointerAreaPaint}
          linkCanvasObject={renderLink}
          onLinkClick={handleLinkClick}
          enableNodeDrag={true}
          enableZoomPanInteraction={true}
        />
      </div>
      {showMore && nodeInfo && nodeInfo.length > 0 && (
        <NodeDrawer
          quote={quote}
          badges={post_type_badges}
          nodeInfo={nodeInfo}
          totalCounts={totalCounts}
          setShowMore={setShowMore}
          influence={true}
        />
      )}
    </>
  );
};

export default memo(GraphContent);
