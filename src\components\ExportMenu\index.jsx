import { memo, useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import {
  DownloadSimple,
  MicrosoftExcelLogo,
  FileJpg,
  FilePng,
} from "@phosphor-icons/react";
import { useChartExport } from "./useChartExport";
import ToolTip from "components/ui/ToolTip";

const ExportMenu = ({
  chartSelector,
  fileName,
  series,
  time,
  excelHeaders,
  onError,
  menuItems = ["PNG", "JPEG", "Excel"],
  className = "",
  chartRef,
  useCanvasExport = false,
  customExcelFormat = null,
  data = null,
  chartTitle,
  // onExportStart,
}) => {
  const { exportChart } = useChartExport({
    chartSelector,
    fileName,
    series,
    time,
    excelHeaders,
    onError,
    chartRef,
    useCanvasExport,
    customExcelFormat,
    data,
    chartTitle,
  });

  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);

  const handleExport = (format) => {
    setIsOpen(false);
    setTimeout(() => {
      exportChart(format);
    }, 300);
  };

  //   const close = () => setIsOpen(false);

  // const handleExport = async (format) => {
  //   close();
  //   await onExportStart(true);
  //   setTimeout(() => {
  //     exportChart(format);
  //   }, 10);
  //   setTimeout(() => {
  //     onExportStart(false);
  //   }, 100);
  // };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const getIcon = (format) => {
    const lower = format.toLowerCase();
    if (lower === "excel")
      return <MicrosoftExcelLogo size={18} className="ml-2" fill="green" />;
    if (lower === "jpeg")
      return <FileJpg size={18} className="ml-2" fill="#207AD6" />;
    if (lower === "png")
      return <FilePng size={18} className="ml-2" fill="#E55153" />;
    return null;
  };

  const getLabel = (format) =>
    `${format.toLowerCase() === "excel" ? "فایل" : "تصویر"} ${format}`;

  return (
    <div
      ref={menuRef}
      className={`relative inline-block text-left ${className}`}
    >
      <ToolTip comp={"دانلود نمودار"} position="right">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="focus:outline-none export-menu-button"
        >
          <DownloadSimple size={20} className="cursor-pointer" />
        </button>
      </ToolTip>

      {isOpen && (
        <div
          className="absolute z-50 left-0 mt-2 w-32 font-body-medium origin-top-right bg-white divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          role="menu"
        >
          {menuItems.map((format) => (
            <button
              key={format}
              onClick={() => {
                handleExport(format);
              }}
              className="w-full flex flex-row-reverse items-center justify-between px-2 py-2 text-sm text-gray-900 hover:bg-gray-100"
              role="menuitem"
            >
              <span className="mr-2">{getIcon(format)}</span>
              {getLabel(format)}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

ExportMenu.propTypes = {
  chartSelector: PropTypes.string.isRequired,
  fileName: PropTypes.string.isRequired,
  series: PropTypes.array.isRequired,
  time: PropTypes.array.isRequired,
  excelHeaders: PropTypes.array,
  onError: PropTypes.func,
  menuItems: PropTypes.arrayOf(PropTypes.string),
  className: PropTypes.string,
  chartRef: PropTypes.object,
  useCanvasExport: PropTypes.bool,
  customExcelFormat: PropTypes.string,
  data: PropTypes.object,
  chartTitle: PropTypes.string,
};

export default memo(ExportMenu);
