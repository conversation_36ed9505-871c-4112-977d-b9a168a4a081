import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import "../../style.css";
import SORT_TYPE from "constants/sort-type.js";
import DropDown from "components/ui/DropDown";
import { useReport360Store } from "store/report360Store";

const ReportsInfo = ({ activeSpan, sort, setSort, onDropdownOpenChange }) => {
  const { profile } = useReport360Store((state) => state.report);
  const platform = profile.platform;

  const getTitle = () => {
    switch (activeSpan) {
      case "overview":
        return "نمای کلی منبع";
      case "contacts":
        return "اطلاعات مخاطبان";
      case "analysis":
        return "تحلیل افکار و عقاید";
      case "source-content":
        return "محتوای منبع";
      default:
        return "";
    }
  };

  const getDescription = () => {
    switch (activeSpan) {
      case "overview":
        return "در این قسمت، اطلاعات کلی منبع را در بستر انتخاب شده مشاهده می‌کنید.";
      case "contacts":
        return (
          <span>
            در این قسمت، داده‌های <strong>فالوور ها (دنبال کننده ها)</strong>{" "}
            مورد بررسی قرار می‌گیرد.
          </span>
        );
      case "analysis":
        return "در این قسمت، بر اساس بررسی داده‌ها توسط هوش مصنوعی، گرایشات فکری و عقاید منبع مورد نظر نمایش داده می‌شود.";
      case "source-content":
        return "در این قسمت، محتوای مرتبط با منبع مورد نظر را بر اساس تاریخ فیلتر شده مشاهده می‌کنید.";
      default:
        return "";
    }
  };

  const handleDropdownOpenChange = (isOpen) => {
    onDropdownOpenChange?.(isOpen);
  };

  return (
    // <Card className="flex justify-between items-center gap-2 border-r-8 border-[#4D36BF] card-animation card-delay my-3">
    //   <div className="flex flex-col gap-2">
    //     <h3 className="font-headline-small">{getTitle()}</h3>
    //     <p className="font-body-small text-[#8f8f8f]">{getDescription()}</p>
    //   </div>
    <div className="flex justify-end pl-6 items-center gap-2 mt-4">
      {activeSpan === "source-content" && (
        <div className="border border-[#d6d6db] rounded-lg !bg-white">
          <DropDown
            title="نمایش بر اساس"
            subsets={SORT_TYPE[platform].map((item) => item.fa)}
            selected={sort.fa}
            setSelected={(value) => {
              setSort(
                SORT_TYPE[platform].filter((item) => item.fa === value)[0]
              );
            }}
            onOpenChange={handleDropdownOpenChange}
          />
        </div>
      )}
    </div>
    // </Card>
  );
};

ReportsInfo.propTypes = {
  activeSpan: PropTypes.string.isRequired,
  sort: PropTypes.shape({
    fa: PropTypes.string.isRequired,
  }).isRequired,
  setSort: PropTypes.func.isRequired,
  onDropdownOpenChange: PropTypes.func,
};

export default ReportsInfo;
