import { Card } from "components/ui/Card.jsx";
import PropTypes from "prop-types";
import ReleaseChart from "components/Charts/LineChart.jsx";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const OverviewProcess = ({ activePlatform, height = false }) => {
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info?.entity,
    process: state.report?.content?.report_info?.process,
  }));

  const platformColors = {
    twitter: "#336699",
    instagram: "#FF69B4",
    news: "orange",
    telegram: "#33AADD",
  };

  const platformPersianNames = {
    twitter: "توییتر",
    instagram: "اینستاگرام",
    telegram: "تلگرام",
    news: "سایت‌های خبری",
  };

  const series = Object.keys(sourceReport.process || [])
    .filter((platform) =>
      activePlatform === "all" ? true : platform === activePlatform
    )
    .map((platform) => {
      return {
        name: `${platformPersianNames[platform]}`,
        data: sourceReport.process[platform].map((item) => item.count || 0),
        time: sourceReport.process[platform].map((item) => item.datetime || ""),
        color: platformColors[platform] || undefined,
      };
    });

  // Prepare series and time for ExportMenu
  const exportSeries = series.map((serie) => ({
    name: serie.name,
    data: serie.data,
    time: serie.time,
  }));

  const time = series.length > 0 ? series[0].time : [];

  return (
    <div className="overview-process-container flex !w-full">
      <Card className={`w-full ${height && `h-[${height}]`}`}>
        <div className="w-full flex flex-col gap-3">
          <div className="flex items-center justify-between">
            <Title title="روند انتشار محتوا" />
            <ExportMenu
              chartSelector=".overview-process-container"
              fileName="overview-process"
              series={exportSeries}
              time={time}
              excelHeaders={["Time", ...exportSeries.map((s) => s.name)]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
            />
          </div>
          <p className="font-body-small text-[#8f8f8f] pr-5">
            در مورد این شخص در منابع مختلف محتوا با چه روندی انتشار یافته است
          </p>
          <ReleaseChart series={series} />
        </div>
      </Card>
    </div>
  );
};

OverviewProcess.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  height: PropTypes.string,
};

export default OverviewProcess;
