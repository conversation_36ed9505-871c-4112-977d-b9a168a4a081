import PropTypes from "prop-types";
import PersonBestSource from "../../components/BestSource";
// import PersonContentNumber from "../../components/ContentNumber";
import LatestContent from "../../components/LatestContent";
// import OwnedResources from "../../components/OwnedResources";
import PersonReleaseProcess from "../../components/ReleaseProcess";
// import PersonRepeatHashtags from "../../components/RepeatHashtags";
import PersonRepeatWords from "../../components/RepeatWords";
import ResourcesInfo from "../../components/ResourcesInfo";
import PersonThematicCategory from "../../components/ThematicCategory";
import PersonEntities from "../../components/entities";

const NewsBased = ({ isUpdate }) => {
  return (
    <div className="flex flex-col gap-3 px-4">
      {/* <OwnedResources activeSpan={activeSpan} /> */}
      <div className="flex w-full items-center pt-5 gap-4">
        <div className="rounded-lg w-2/6">
          <ResourcesInfo activePlatform={"news"} isUpdate={isUpdate} />
        </div>
        <div className="rounded-lg p-3 bg-white shadow-sm w-4/6">
          <PersonReleaseProcess
            activePlatform={"news"}
            height="26rem"
            isUpdate={isUpdate}
          />
        </div>
      </div>
      <LatestContent activePlatform={"news"} isUpdate={isUpdate} />
      {/* <div className="w-2/5">
          <PersonContentNumber activeSpan={activeSpan} />
        </div> */}
      <div className="flex gap-3 items-stretch">
        <div className="w-2/6">
          <PersonRepeatWords activePlatform={"news"} isUpdate={isUpdate} />
        </div>
        <div className="w-4/6">
          <PersonThematicCategory activePlatform={"news"} isUpdate={isUpdate} />
        </div>
      </div>
      <div className="flex gap-3 items-stretch">
        <div className="w-3/5">
          <PersonBestSource activePlatform={"news"} isUpdate={isUpdate} />
        </div>
        <div className="w-2/5">
          <PersonEntities activePlatform={"news"} isUpdate={isUpdate} />
        </div>
      </div>
    </div>
  );
};

NewsBased.propTypes = {
  isUpdate: PropTypes.bool,
};

export default NewsBased;
