import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { SpinnerGap } from "@phosphor-icons/react";
import { toPersianNumber } from "utils/helper.js";

const MentionChart = ({ colors, info, loading }) => {
  if (loading) {
    return (
      <div className="w-full h-40 flex justify-center items-center">
        <SpinnerGap size={40} className="animate-spin" />
      </div>
    );
  }

  const seriesData = [
    {
      name: "سال 1403",
      data: info?.slice(0, 10).map((value, index) => ({
        y: value?.count ?? 0,
        color: colors[index % colors.length],
      })),
    },
  ];

  const chartOptions = {
    chart: {
      type: "bar",
      height: "60%",
    },
    title: {
      text: null,
      align: "right",
      enabled: false,
    },
    subtitle: {
      text: null,
    },
    xAxis: {
      categories: info?.map(
        (item) =>
          `<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(175px, 1fr)); gap: 10px;">
    <div style="display: flex; align-items: center; gap: 5px;">
        <div style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px; border-radius: 50%; overflow: hidden;">
            <img src="${item.avatar}" alt="${item.word}" style="width: 100%; height: 100%; object-fit: cover;" />
        </div>
        <div style="text-align: left;">
            <strong style="font-size: 16px;">${item.word}</strong><br/>
            <span style="color:#d3d3d3;">@${item.username}</span>
        </div>
    </div>
</div>
`,
      ),
      labels: {
        useHTML: true,
        formatter: function () {
          return this.value;
        },
      },
      title: {
        text: null,
      },
      gridLineWidth: 0,
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
        align: "right",
      },
      labels: {
        enabled: false,
        overflow: "justify",
      },
      gridLineWidth: 0,
    },
    tooltip: {
      formatter: function () {
        return `${toPersianNumber(this.y)}`;
      },
    },
    plotOptions: {
      bar: {
        borderRadius: "50%",
        dataLabels: {
          enabled: true,
          formatter: function () {
            return toPersianNumber(this.y);
          },
        },
        pointPadding: 0.2,
        groupPadding: 0.1,
        colorByPoint: true,
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    series: seriesData,
  };

  return (
    <div className="min-w-[19rem] w-full">
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </div>
  );
};

MentionChart.propTypes = {
  data: PropTypes.object.isRequired,
  type: PropTypes.string,
  colors: PropTypes.arrayOf(PropTypes.string).isRequired,
  info: PropTypes.arrayOf(
    PropTypes.shape({
      count: PropTypes.number,
    }),
  ).isRequired,
  loading: PropTypes.bool.isRequired,
  categories: PropTypes.arrayOf(PropTypes.string),
};

export default MentionChart;
