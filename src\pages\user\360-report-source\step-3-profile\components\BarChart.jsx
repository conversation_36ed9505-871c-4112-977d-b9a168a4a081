import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { useCompareStore } from "store/compareStore";
import { useEffect, useState } from "react";
import { shortener, toPersianNumber } from "utils/helper.js";

const BarChart = ({ categories = [], data = [], title }) => {
  const [num, setNum] = useState(0);
  const { fields } = useCompareStore((state) => state.compare);

  const chartOptions = {
    chart: {
      type: "column",
      height: "100%",
      backgroundColor: "#F0F2F6",
      style: {
        borderRadius: "8px",
      },
    },
    title: {
      text: title ? title : null,
      align: "right",
      style: {
        fontSize: "15px",
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'IranYekan';font-size: 13px;">`;
        this.points.forEach((point) => {
          tooltipHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                point.color
              }; margin-right: 8px;"></div>
              <span>${shortener(point.series.name, 12, "rtl")}:</span> 
              <span style="color: #333; margin-left: 5px;">${toPersianNumber(
                point.y,
              )}</span>
            </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    xAxis: {
      categories: categories ? categories : null,
      crosshair: true,
      accessibility: {
        description: "categories",
      },
      visible: false,
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
    },
    plotOptions: {
      column: {
        borderWidth: 0,
        groupPadding: 0.2,
        pointPadding: 0.1,
      },
      series: {
        enableMouseTracking: true,
        borderRadius: {
          radius: 11,
        },
      },
    },
    series: [
      {
        name: data[0]?.name,
        data: data[0]?.data,
        color: "#432FA7",
      },
      {
        name: data[1]?.name,
        data: data[1]?.data,
        color: "gray",
      },
      {
        name: data[2]?.name,
        data: data[2]?.data,
        color: "#D9D5ED",
      },
    ],
  };

  useEffect(() => {
    setNum((l) => l + 1);
  }, [fields]);

  return (
    <div className="w-full">
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        key={num}
      />
    </div>
  );
};

BarChart.propTypes = {
  categories: PropTypes.array,
  data: PropTypes.array.isRequired,
  title: PropTypes.string.isRequired,
};

export default BarChart;
