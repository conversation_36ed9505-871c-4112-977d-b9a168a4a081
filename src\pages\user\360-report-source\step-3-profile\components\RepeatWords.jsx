import { useState, useEffect, memo } from "react";
import { Cloud, FunnelSimple, SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import { preprocessWord } from "utils/helper";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import RepeatWordsChart from "./repeatWordsChart";
import { Tabs360 } from "./360tabs";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";
import { toPersianNumber } from "utils/helper";

const RepeatWords = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    frequent_words: state.report?.content?.report_info?.frequent_words,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const platform = sourceReport.platform;
  const profile = sourceReport.profile;
  const sourceId =
    profile.user_name ||
    profile.channel_id ||
    profile.source_name ||
    profile.id;

  const [categories, setCategories] = useState([]);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("cluster");
  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [info, setInfo] = useState([]);

  const fetchProfileData = async () => {
    const filters = {
      date,
      platform,
      sources: [sourceId.toString()],
    };

    const infoQuery = buildRequestData(filters, "cloud");
    const response = await advanceSearch.search(infoQuery);

    return response.data.data?.[platform];
  };

  const updateState = (result) => {
    const innerArray = result;

    if (Array.isArray(innerArray)) {
      const updatedWordClouds =
        innerArray.map(({ key, count }) => ({
          text: preprocessWord(key), // Assuming preprocessWord is defined
          value: count,
        })) || [];

      setWordCloudData(updatedWordClouds);

      setInfo(innerArray);

      setCategories(innerArray.slice(0, 10).map(({ key }) => key));

      const payload = innerArray.map(({ key, count }) => ({
        key,
        count,
      }));

      updateReportField("content.report_info.frequent_words", payload);
    } else {
      console.warn("Expected an array inside `result`.");
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      const result = await fetchProfileData();

      updateState(result);
    } catch (error) {
      console.error("Error fetching data:", error);
      setWordCloudData([]);
      setInfo([]);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if data is cached and not updating
    // if (chartData[chartKey] && !isUpdate) {
    //   setWordCloudData(chartData[chartKey].wordCloudData);
    //   setInfo(chartData[chartKey].info);
    //   setCategories(chartData[chartKey].categories);
    //   setLoading(false);
    // } else if (!isUpdate) {
    //   // setWordCloudData(new Array(profileCleared?.length).fill([]));

    // }
    getData();
  }, [date]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  return (
    <div className="flex !h-full">
      <Card className="px-0 card-animation card-delay !h-full">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="w-1/2">
              <Title title="کلمات پر تکرار" />
            </div>
            <div className="flex w-1/2">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 !h-full">
            {loading ? (
              <div className="flex w-full h-full justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : info.length ? (
              <>
                {wordCloudActiveTab === "wordCloud" && info.length > 0 && (
                  <div className="flex flex-1 responsive-svg h-[26rem]">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={wordCloudData}
                      callbacks={{
                        getWordTooltip: (word) => {
                          return `${word.text} (${toPersianNumber(
                            word.value
                          )})`;
                        },
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && info.length > 0 && (
                  <RepeatWordsChart
                    colors={info.map((item) => getGreenColor(item.count))}
                    info={info}
                    categories={categories}
                    loading={loading}
                  />
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};
export default memo(RepeatWords);
