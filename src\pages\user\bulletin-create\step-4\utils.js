import Bulletin from "service/api/bulletin";

export const fetchBulletinTemplates = async (platform = "all") => {
  try {
    const res = await Bulletin.getTemplates();
    return res?.data?.data || [];
  } catch (error) {
    console.error("Bulletin Templates Fetch Error:", error);
    throw error;
  }
};

export const createBulletinTemplate = async (templateData) => {
  try {
    const res = await Bulletin.createTemplate(templateData);
    return res?.data || {};
  } catch (error) {
    console.error("Create Bulletin Template Error:", error);
    throw error;
  }
};
