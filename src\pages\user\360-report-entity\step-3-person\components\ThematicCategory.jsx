import MultipleBar from "./MultipleBar";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import "../../style.css";
import PropTypes from "prop-types";
import { SUBJECT_CATEGORIES } from "constants/subject-category.js";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";
import { memo } from "react";

const PersonThematicCategory = ({ activePlatform }) => {
  const sourceReport = use360requestStore((state) => ({
    categories: state.report?.content?.report_info?.categories,
  }));

  const data = sourceReport.categories?.[activePlatform] || [];

  const transformedData = data.map((item) => ({
    key: item.key,
    count: item.count,
  }));

  const categories = transformedData.map(
    (item) =>
      SUBJECT_CATEGORIES.find((category) => category.value === item.key)?.label
  );

  const total = transformedData.reduce((sum, item) => sum + item.count, 0);

  const seriesData = [
    {
      name: "Count",
      data: transformedData.map((item) =>
        parseFloat(((item.count / total) * 100).toFixed(2))
      ),
      color: "#7d6cd5",
    },
  ];

  // Prepare series and time for ExportMenu
  const series = data.map((serie) => ({
    name: serie.name,
    data: serie.data,
    time: categories,
  }));

  const time = categories;

  return (
    <div className="flex w-full">
      <Card className="!p-6 card-animation card-delay">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between">
            <Title title="دسته‌بندی موضوعی" />
            <ExportMenu
              chartSelector=".person-thematic-category-container"
              fileName="person-thematic-category"
              series={series}
              time={time}
              excelHeaders={["Category", "Percentage"]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
              chartTitle="دسته‌بندی موضوعی"
            />
          </div>
          <div className="person-thematic-category-container">
            {data?.length ? (
              <MultipleBar
                categories={categories}
                data={seriesData}
                groupPadding={0.3}
              />
            ) : (
              <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
                داده‌ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

PersonThematicCategory.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default memo(PersonThematicCategory);
