import { useEffect, useState, useRef } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import markerIcon from "leaflet/dist/images/marker-icon.png";
import markerIcon2x from "leaflet/dist/images/marker-icon-2x.png";
import markerShadow from "leaflet/dist/images/marker-shadow.png";
import { Card } from "components/ui/Card";
import provinceGeoJson from "../../../../../../assets/geojson/iran_provinces.json";
import cityJson from "../../../../../../assets/geojson/iran_cities.json";
import iranBorder from "../../../../../../assets/geojson/iran_border.json";
import ProvincesList from "./ProvincesList";
import "../style.css";
import MapEventsHandler from "./MapEvent";

const IranMap = ({ onCityName }) => {
  const [provinceData, setProvinceData] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  const [cityGeoJson, setCityGeoJson] = useState(null);
  const [clickedCityHover, setClickedCityHover] = useState(null);
  const [hoveredProvince, setHoveredProvince] = useState(null);
  const [zoomedProvince, setZoomedProvince] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(5);
  const [tileMode, setTileMode] = useState("street");

  const mapRef = useRef(null);

  useEffect(() => {
    setProvinceData(provinceGeoJson);

    const cityFeatures = cityJson.map((city) => ({
      type: "Feature",
      properties: { name: city.city, province: city.province },
      geometry: {
        type: "Point",
        coordinates: [parseFloat(city.longitude), parseFloat(city.latitude)],
      },
    }));
    setCityGeoJson({ type: "FeatureCollection", features: cityFeatures });

    delete L.Icon.Default.prototype._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconUrl: markerIcon,
      iconRetinaUrl: markerIcon2x,
      shadowUrl: markerShadow,
    });
  }, []);

  useEffect(() => {
    if (zoomedProvince && mapRef.current) {
      const province = provinceData.features.find(
        (feature) => feature.properties.name === zoomedProvince
      );

      if (province) {
        const bounds = L.geoJSON(province).getBounds();
        mapRef.current.fitBounds(bounds, { padding: [50, 50] });
      }
    }
  }, [zoomedProvince, provinceData]);

  const provinceStyle = (feature) => ({
    color: hoveredProvince === feature.properties.name ? "#432fa7" : "#3388ff",
    weight: hoveredProvince === feature.properties.name ? 3 : 1,
    opacity: 1,
    fillColor:
      hoveredProvince === feature.properties.name ? "#d4b7ff" : "#7d6cd5",
    fillOpacity: hoveredProvince === feature.properties.name ? 0.5 : 0.1,
  });

  const onEachProvinceFeature = (feature, layer) => {
    layer.on({
      mouseover: () => {
        if (zoomLevel <= 10) {
          setHoveredProvince(feature.properties.name);
        }
      },
      // mouseout: () => {
      //   setHoveredProvince(null);
      // },
      click: () => {
        setHoveredProvince(feature.properties.name);
        setZoomedProvince(feature.properties.name);
      },
    });

    layer.bindTooltip(feature.properties.name, {
      permanent: false,
      direction: "top",
    });
  };

  useEffect(() => {
    if (zoomLevel >= 10) {
      setHoveredProvince(null);
    }
  }, [zoomLevel]);

  const smallIcon = new L.Icon({
    iconUrl: markerIcon,
    iconSize: [12, 18],
    iconAnchor: [8, 8],
    popupAnchor: [0, -10],
  });

  const onEachCityFeature = (feature, layer) => {
    layer.bindTooltip(
      `<span class="font-subtitle-medium">${feature.properties.name}</span>`,
      {
        permanent: false,
        direction: "top",
        offset: [47, -10],
      }
    );

    layer.on("click", () => {
      if (onCityName) {
        onCityName(feature.properties.name);
      }
      setSelectedCity(feature.properties.name);
      setClickedCityHover(true);
    });
  };

  const handleCityClick = (cityData) => {
    if (mapRef.current && cityData.latitude && cityData.longitude) {
      const latitude = cityData.latitude;
      const longitude = cityData.longitude;

      mapRef.current.setView([latitude, longitude], 12, { animate: true });
    } else {
      console.error("Invalid city data or map reference");
    }
  };

  const STREET_TILE_URL = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png";
  const SATELLITE_TILE_URL =
    "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";

  useEffect(() => {
    if (!mapRef.current) return;
    const map = mapRef.current;

    const handleZoom = () => {
      const currentZoom = map.getZoom();
      setZoomLevel(currentZoom);
      // setTileMode(currentZoom >= 10 ? "satellite" : "street");
    };

    const handleMapLoad = () => {
      const initialZoom = map.getZoom();
      setZoomLevel(initialZoom);
      // setTileMode(initialZoom >= 10 ? "satellite" : "street");
    };

    map.on("zoomend", handleZoom);
    map.whenReady(handleMapLoad);

    return () => {
      map.off("zoomend", handleZoom);
    };
  }, []);

  return (
    <Card className="flex items-center w-full mapCard-container">
      <div className="flex !gap-5 !flex-row !w-full">
        <div className="!w-3/5">
          <MapContainer
            center={[32.0, 53.6]}
            zoom={5}
            className="map-container"
            maxBounds={[
              [24.0, 44.0],
              [40.5, 63.5],
            ]}
            maxBoundsViscosity={1.0}
            minZoom={5.2}
            maxZoom={16}
            ref={mapRef}
            attributionControl={false}
          >
            <MapEventsHandler
              setZoomLevel={setZoomLevel}
              setTileMode={setTileMode}
            />
            <TileLayer
              key={tileMode}
              url={
                // tileMode === "satellite" ? SATELLITE_TILE_URL : STREET_TILE_URL
                STREET_TILE_URL
              }
              attribution={
                tileMode === "satellite"
                  ? '&copy; <a href="https://www.esri.com/en-us/home">Esri</a>'
                  : '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
              }
            />

            <GeoJSON
              data={iranBorder}
              style={{
                color: "",
                weight: 2,
                fillColor: "lightgray",
                fillOpacity: 0.5,
              }}
            />
            {provinceData && (
              <GeoJSON
                data={provinceData}
                style={provinceStyle}
                onEachFeature={onEachProvinceFeature}
              />
            )}
            {cityGeoJson && hoveredProvince && (
              <GeoJSON
                key={hoveredProvince}
                data={{
                  type: "FeatureCollection",
                  features: cityGeoJson.features.filter(
                    (city) =>
                      city.properties.province === hoveredProvince &&
                      city.geometry.coordinates &&
                      !isNaN(city.geometry.coordinates[0]) &&
                      !isNaN(city.geometry.coordinates[1])
                  ),
                }}
                pointToLayer={(feature, latlng) =>
                  L.marker(latlng, { icon: smallIcon })
                }
                onEachFeature={onEachCityFeature}
              />
            )}
          </MapContainer>
        </div>
        <div className="!w-2/5">
          <ProvincesList
            setHoveredProvince={setHoveredProvince}
            setZoomedProvince={setZoomedProvince}
            onCityClick={handleCityClick}
            onCityName={onCityName}
            hoveredProvince={hoveredProvince}
            clickedCity={selectedCity}
            clickedCityHover={clickedCityHover}
            setClickedCityHover={setClickedCityHover}
          />
        </div>
      </div>
    </Card>
  );
};

IranMap.propTypes = {
  onCityName: PropTypes.func.isRequired,
};

export default IranMap;
