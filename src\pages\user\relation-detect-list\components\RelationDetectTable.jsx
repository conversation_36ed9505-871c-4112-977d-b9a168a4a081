import { Link, useNavigate } from "react-router-dom";
import {
  notification,
  parseTimeTo<PERSON><PERSON><PERSON>,
  toPersian<PERSON>umber,
} from "utils/helper";
import ToolTip from "components/ui/ToolTip";
import {
  CheckCircle,
  Eye,
  ShareNetwork,
  TrashSimple,
} from "@phosphor-icons/react";
import DeletePopUp from "components/ui/DeletePopUp";
import { useState } from "react";
import relationDetect from "service/api/relationDetect";

const TicketsTable = ({ data, getList }) => {
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [selectedGraphId, setSelectedGraphId] = useState(null);
  const navigate = useNavigate();
  const deleteHandler = async () => {
    if (!selectedGraphId) return;
    try {
      await relationDetect.deleteGraph(selectedGraphId);
      notification.success(
        "گراف با موفقیت حذف شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
      setIsDeletePopUpOpen(false);
      getList();
    } catch (error) {
      console.error("Error deleting graph:", error);
      notification.error("خطا در حذف گراف");
    } finally {
      setSelectedGraphId(null);
    }
  };

  const fetchGraphData = async (id) => {
    try {
      const res = await relationDetect.getGraph(id);
      navigate("/app/relation-detect/results", {
        state: {
          finalGraphData: res?.data?.data?.params,
          date: {
            start_date: res?.data?.data?.query?.start_date,
            end_date: res?.data?.data?.query?.end_date,
          },
        },
      });
    } catch (error) {
      console.error("Error deleting graph:", error);
      notification.error("خطا");
    }
  };

  const handleDeleteClick = (id) => {
    setSelectedGraphId(id);
    setIsDeletePopUpOpen(true);
  };

  return (
    <>
      <div className="grid grid-cols-6 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer">
        <div className="flex font-body-medium">
          {data?.title || "بدون عنوان"}
        </div>

        <div className="font-body-medium">
          {toPersianNumber(data?.node_count || 0)}
        </div>

        <div className="font-body-medium">
          <p>
            {data?.query?.connection_mode === "direct" ? "مستقیم" : "غیرمستقیم"}
          </p>
        </div>

        <div className="font-body-medium">
          <p>{toPersianNumber(data?.query?.node_weight || 0)}</p>
        </div>

        <div className="font-body-medium">
          <p>{parseTimeToPersian(data?.created_at) || "نامشخص"}</p>
        </div>

        <div className="font-body-medium">
          <div className="flex gap-4">
            <div
              onClick={() => fetchGraphData(data?.id)}
              // to="/app/relation-detect/results"
              // state={{ graphData: data?.query }}
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            >
              <ToolTip comp="نمایش جزئیات">
                <Eye size={16} />
              </ToolTip>
            </div>
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => handleDeleteClick(data?.id)}
            >
              <ToolTip comp="حذف">
                <TrashSimple size={16} />
              </ToolTip>
            </div>
            <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
              <ToolTip comp="اشتراک">
                <ShareNetwork size={16} />
              </ToolTip>
            </div>
          </div>
        </div>
      </div>
      <DeletePopUp
        onClose={() => {
          setIsDeletePopUpOpen(false);
          setSelectedGraphId(null);
        }}
        isOpen={isDeletePopUpOpen}
        submitHandler={deleteHandler}
        title="آیا می‌خواهید گراف مورد نظر را حذف کنید؟"
      />
    </>
  );
};

export default TicketsTable;
