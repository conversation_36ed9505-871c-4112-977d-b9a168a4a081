import { useEffect, useState, memo } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { sentiment } from "utils/selectIcon";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import PropTypes from "prop-types";
import "../../style.css";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import ExportMenu from "components/ExportMenu/index.jsx";

const Sentiment = ({ showDataLabels = false }) => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    sentiments: state.report?.content?.report_info?.sentiments,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const profile = sourceReport.profile;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [rawCounts, setRawCounts] = useState({
    positive: 0,
    neutral: 0,
    negative: 0,
  });
  const [chartDataLocal, setChartDataLocal] = useState({
    positive: 0,
    neutral: 0,
    negative: 0,
  });

  const calculatePercentages = (result, total) => {
    if (total === 0) return { negative: 0, neutral: 0, positive: 0 };

    return {
      negative: Math.round((result.negative / total) * 100),
      neutral: Math.round((result.neutral / total) * 100),
      positive: Math.round((result.positive / total) * 100),
    };
  };

  const processResult = (item, total) => {
    let result = {
      negative: 0,
      neutral: 0,
      positive: 0,
    };

    item?.forEach(({ key, count }) => {
      if (key === "negative") {
        result.negative = count || 0;
      } else if (key === "neutral") {
        result.neutral = count || 0;
      } else if (key === "positive") {
        result.positive = count || 0;
      }
    });

    const percentages = calculatePercentages(result, total);

    // const transformedResult = [
    //   { key: "neutral", count: result.neutral },
    //   { key: "positive", count: result.positive },
    //   { key: "negative", count: result.negative },
    // ];

    return { ...result, ...percentages };
  };

  const getData = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const filterObject = {
        date,
        platform: sourceReport.platform,
        sources: [sourceId.toString()],
      };

      const requestData = buildRequestData(filterObject, "sentiments");
      const result = await advanceSearch.search(requestData);

      const totalData = result?.data?.data?.total || 0;
      const chartData = result?.data?.data?.[sourceReport.platform] || [];

      updateReportField("content.report_info.sentiments", {
        chartData: chartData,
        total: totalData,
      });

      const sentimentCounts = {
        negative: 0,
        neutral: 0,
        positive: 0,
      };
      chartData?.forEach(({ key, count }) => {
        if (key === "negative") sentimentCounts.negative = count;
        else if (key === "neutral") sentimentCounts.neutral = count;
        else if (key === "positive") sentimentCounts.positive = count;
      });

      const parsedResult = processResult(chartData, totalData);
      setRawCounts(sentimentCounts);
      setTotal(totalData);
      setChartDataLocal(parsedResult);
    } catch (error) {
      console.error("Error fetching data:", error);
      setChartDataLocal({ positive: 0, neutral: 0, negative: 0 });
      setRawCounts({ positive: 0, neutral: 0, negative: 0 });
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Sentiment",
      data: [
        chartDataLocal.positive,
        chartDataLocal.neutral,
        chartDataLocal.negative,
      ],
      time: ["مثبت", "خنثی", "منفی"],
    },
  ];

  const time = ["مثبت", "خنثی", "منفی"];

  useEffect(() => {
    if (
      sourceReport.sentiments &&
      typeof sourceReport.sentiments === "object" &&
      Object.keys(sourceReport.sentiments).length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      const sentimentCounts = {
        negative: 0,
        neutral: 0,
        positive: 0,
      };
      sourceReport.sentiments?.chartData?.forEach(({ key, count }) => {
        if (key === "negative") sentimentCounts.negative = count;
        else if (key === "neutral") sentimentCounts.neutral = count;
        else if (key === "positive") sentimentCounts.positive = count;
      });

      const parsedResult = processResult(
        sourceReport.sentiments.chartData,
        sourceReport.sentiments.total
      );
      setRawCounts(sentimentCounts);
      setTotal(sourceReport.sentiments.total);
      setChartDataLocal(parsedResult);
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [date]);

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay !h-full">
      <div className="flex items-center justify-between">
        <p className="font-subtitle-large text-right">تحلیل احساسات</p>
        <ExportMenu
          chartSelector=".sentiment-container"
          fileName="sentiment-data"
          series={series}
          time={time}
          excelHeaders={["Sentiment", "Percentage"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="تحلیل احساسات"
        />
      </div>
      <Divider />
      {loading ? (
        <div className="w-full !h-full flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : total > 0 ? (
        <div className="flex sentiment-container h-full items-center">
          <Doughnut
            showDataLabels={showDataLabels}
            name="sentiment"
            height={240}
            data={[
              {
                name: "مثبت",
                y: chartDataLocal.positive,
                count: rawCounts.positive,
              },
              {
                name: "خنثی",
                y: chartDataLocal.neutral,
                count: rawCounts.neutral,
              },
              {
                name: "منفی",
                y: chartDataLocal.negative,
                count: rawCounts.negative,
              },
            ]}
            legendFormatter={function () {
              return `<div dir="rtl" style="font-family: iranyekan; display: flex; gap: 30%; width: 100%; padding: 2px; align-items: center; margin-right: 2rem;">
              <span style="font-size: 16px; justify-self: start;">
              ${toPersianNumber(this.y)}%
              </span>
              <div style="display: flex; gap: 8px; align-items: center; padding-right: 1rem;">
              <span style="color: ${this.color}; font-size: 14px;">
              ${this.name}
              </span> 
              <img src=${
                sentiment[this.name]
              } style="width: 20px; height: 20px; margin-right: 15px;" />
                </div>
                </div>`;
            }}
            tooltipFormatter={function () {
              return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan">
                <div>${this.key}</div>
                <div>${toPersianNumber(
                  formatShortNumber(this.point.count)
                )}</div>
                  </div>`;
            }}
            colors={["#1CB0A5", "#00000080", "#E0526A"]}
          />
        </div>
      ) : (
        <div className="h-80 flex items-center w-full justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      )}
    </Card>
  );
};

Sentiment.propTypes = {
  showDataLabels: PropTypes.bool,
};

export default memo(Sentiment);
