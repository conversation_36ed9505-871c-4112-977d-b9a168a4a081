import { Card } from "components/ui/Card";
import DropDown from "components/ui/DropDown";
import CLUSTER_COLORS from "constants/colors";
import PropTypes from "prop-types";
import { useState } from "react";
import ReactWordcloud from "react-wordcloud";
import { toPersianNumber } from "utils/helper";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const PersonEntities = ({ activePlatform }) => {
  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");
  const [activeTab, setActiveTab] = useState("اشخاص");
  const [activeTabName, setActiveTabName] = useState("person");

  const sourceReport = use360requestStore((state) => ({
    entities: state.report?.content?.report_info?.entities,
  }));

  const data = sourceReport.entities?.[activePlatform] || [];

  const updatedEntities = {
    person: data.person || [],
    location: data.location || [],
    event: data.event || [],
    organ: data.organ || [],
  };

  const arabicRange = /[\u0600-\u06FF]/;

  const wordCloudData = (updatedEntities[activeTabName] || []).map(
    ({ key, count }) => {
      let formattedKey = key;
      if (!arabicRange.test(key)) {
        if (key?.startsWith("#")) {
          formattedKey = key.slice(1) + "#";
        }
      }
      return { text: formattedKey, value: count };
    }
  );

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Entity Count",
      data: wordCloudData.map((item) => item.value),
      time: wordCloudData.map((item) => item.text),
    },
  ];

  const time = wordCloudData.map((item) => item.text);

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  const tabs = [
    { id: "event", title: "رویداد" },
    { id: "location", title: "مکان ها" },
    { id: "organ", title: "سازمان ها" },
    { id: "person", title: "اشخاص" },
  ];

  return (
    <>
      <Card className="px-0 !h-full card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row items-center gap-5 w-full justify-between">
            <div className="flex items-center w-full">
              <Title title="موجودیت های پرتکرار"></Title>
              <ExportMenu
                chartSelector=".person-entities-container"
                fileName="person-entities"
                series={series}
                time={time}
                excelHeaders={["Entity", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
              />
            </div>
            <div className="tabs-360 gap-1 px-2 w-3/6 font-overline-medium">
              {tabs.map((tab, i) => (
                <span
                  key={i}
                  onClick={() => {
                    setActiveTabName(tab.id);
                    setActiveTab(tab.title);
                  }}
                  className={`duration-200 text-center !w-[8rem] cursor-pointer p-2 rounded ${
                    activeTab === tab.title
                      ? "text-[#6F5CD1] bg-[#E9E6F7]"
                      : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
                  }`}
                >
                  {tab.title}
                </span>
              ))}
            </div>

            <div className="dropdown-360 w-2/5">
              <DropDown
                title="انتخاب موجودیت"
                subsets={tabs.map((tab) => tab.title)}
                selected={activeTab}
                setSelected={(value) => {
                  const selectedTab = tabs.find((tab) => tab.title === value);
                  if (selectedTab) {
                    setActiveTabName(selectedTab.id);
                    setActiveTab(value);
                  }
                }}
              />
            </div>
          </div>

          {wordCloudData.length === 0 && (
            <div className="h-full flex items-center justify-center font-subtitle-medium pt-40">
              داده ای برای نمایش وجود ندارد
            </div>
          )}

          <div className="grid grid-cols-1 h-full">
            <div className="flex flex-1 responsive-svg h-full">
              <ReactWordcloud
                options={{
                  rotations: 1,
                  rotationAngles: [0],
                  enableTooltip: true,
                  deterministic: false,
                  fontFamily: "iranyekan",
                  fontSizes: [14, 54],
                  padding: 10,
                  colors: CLUSTER_COLORS,
                  tooltipOptions: { theme: "light", arrow: true },
                }}
                words={wordCloudData}
                callbacks={{
                  getWordTooltip: (word) => {
                    const countInPersian = toPersianNumber(word?.value);
                    return `${word.text} (${countInPersian})`;
                  },
                  onWordClick: handleWordClick,
                }}
              />
            </div>
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent word={word} />
        </Drawer>
      )}
    </>
  );
};

export default PersonEntities;

PersonEntities.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};
