import { useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import { MagnifyingGlass } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton.jsx";
import { useReport360Store } from "store/report360Store.js";
import DateFilter from "./DateFilter.jsx";
import PropTypes from "prop-types";
import use360requestStore from "store/360requestStore.js";

const SearchBar = ({ search, loading = false, isFromEntity = false }) => {
  const { type, date } = useReport360Store((state) => state.report);
  const setReport = useReport360Store((state) => state.setReport);
  const updateReport = use360requestStore((state) => state.updateReportField);

  const [searchValue, setSearchValue] = useState();

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setReport({ date: { from: from, to: to } });
    updateReport("start_date", from);
    updateReport("end_date", to);
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      await handleSubmit();
    }
  };

  const handleSubmit = async () => {
    if (isFromEntity) {
      await updateReport("q", searchValue);
    }
    await search(searchValue);
  };

  return (
    <div className="font-body-medium mx-5 h-full bg-white p-5 overflow-hidden rounded-lg shadow-md z-[-1]">
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"lg"}
          validation={"none"}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={"نام حساب کاربری را جست‌و‌جو کنید"}
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={handleSubmit}
          size={"lg"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          استعلام
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        <DateFilter
          type={type}
          handleDateChange={handleDateChange}
          selectedDateRange={date}
        />

        <div>
          <span className="flex gap-2 items-center transition duration-200 rounded-lg p-2">
            نوع استعلام:
            <span className="text-[#7f7f7f]">
              {type === "profile"
                ? "منبع"
                : type === "topic"
                ? "موجودیت"
                : "مکان"}
            </span>
            {/*<CaretLeft />*/}
          </span>
        </div>
      </div>
    </div>
  );
};

SearchBar.propTypes = {
  search: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  isFromEntity: PropTypes.bool,
};

export default SearchBar;
