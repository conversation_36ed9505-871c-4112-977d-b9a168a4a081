import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";

export const fetchStatisticalData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "statistical");
    const res = await advanceSearch.search(requestData);
    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };
    return platformData;
  } catch (error) {
    console.error("Statistical Data Fetch Error:", error);
    throw error;
  }
};

export const fetchProcessData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ q, date, platform }, "process");
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Process Data Fetch Error:", error);
    throw error;
  }
};

export const fetchContentTypeDistributionData = async ({
  date,
  q,
  platform = "all",
}) => {
  try {
    const requestData = buildRequestData(
      { date, platform, q },
      "content_type_distribution"
    );
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Content Type Distribution Data Fetch Error:", error);
    throw error;
  }
};

export const fetchSentimentsData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "sentiments");
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Content Type Distribution Data Fetch Error:", error);
    throw error;
  }
};

export const fetchCategoriesData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "categories");
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Category Data Fetch Error:", error);
    throw error;
  }
};

export const fetchTopSourcesData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "top_sources");
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Top sources Data Fetch Error:", error);
    throw error;
  }
};

export const fetchEntitiesData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "cloud", 25);
    const res = await advanceSearch.search(requestData, null, {
      cloud_type: "entities",
    });

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Entities Data Fetch Error:", error);
    throw error;
  }
};

export const fetchHashtagsData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "cloud", 20);
    const res = await advanceSearch.search(requestData, null, {
      cloud_type: "hashtags",
    });

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Hashtags Data Fetch Error:", error);
    throw error;
  }
};

export const fetchWordsData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "cloud", 20);
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Words Data Fetch Error:", error);
    throw error;
  }
};

export const fetchReport360EntityData = async ({
  date,
  q,
  platform = "all",
}) => {
  try {
    const [
      statisticalData,
      processData,
      content_type_distributionData,
      sentimentsData,
      categoriesData,
      top_sourcesData,
      entitiesData,
      hashtagsData,
      wordsData,
    ] = await Promise.all([
      fetchStatisticalData({ date, q, platform }),
      fetchProcessData({ date, q, platform }),
      fetchContentTypeDistributionData({ date, q, platform }),
      fetchSentimentsData({ date, q, platform }),
      fetchCategoriesData({ date, q, platform }),
      fetchTopSourcesData({ date, q, platform }),
      fetchEntitiesData({ date, q, platform }),
      fetchHashtagsData({ date, q, platform }),
      fetchWordsData({ date, q, platform }),
    ]);

    return {
      statistical: statisticalData,
      process: processData,
      content_type_distribution: content_type_distributionData,
      sentiments: sentimentsData,
      categories: categoriesData,
      top_sources: top_sourcesData,
      entities: entitiesData,
      hashtags: hashtagsData,
      words: wordsData,
    };
  } catch (error) {
    console.error("Report 360 Entity Data Fetch Error:", error);
    throw error;
  }
};

export const isCachedDataValid = (cachedDate, currentDate) => {
  return (
    cachedDate?.from === currentDate?.from && cachedDate?.to === currentDate?.to
  );
};

export const hasRequiredCachedData = (sourceReport) => {
  return sourceReport?.statistical?.twitter && sourceReport?.process?.twitter;
};
