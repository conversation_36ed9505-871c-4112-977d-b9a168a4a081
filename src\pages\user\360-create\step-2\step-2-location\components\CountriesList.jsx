import PropTypes from "prop-types";
import { useEffect, useRef } from "react";

const CountriesList = ({
  countries,
  onCountryClick,
  hoveredCountry,
  setHoveredCountry,
  shouldAutoScroll,
  handleHoverList,
  setZoomedCountry,
  zoomedCountry,
  handleMouseOutList,
}) => {
  const listRef = useRef(null);

  useEffect(() => {
    if ((shouldAutoScroll || zoomedCountry) && listRef.current) {
      const targetCountry = zoomedCountry || hoveredCountry;
      const targetElement = listRef.current.querySelector(
        `[data-country="${targetCountry}"]`
      );

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  }, [hoveredCountry, shouldAutoScroll, zoomedCountry]);

  return (
    <div
      className="w-full border-gray-200 mapList-container overflow-y-auto"
      ref={listRef}
      onMouseEnter={() => setHoveredCountry(null)}
    >
      <ul className="divide-y divide-gray-200 text-right">
        {countries.map((country, index) => (
          <li
            key={index}
            data-country={country.name_fa}
            onClick={() => {
              onCountryClick(country.name_fa);
              setZoomedCountry(country.name_fa);
            }}
            onMouseEnter={() => handleHoverList(country.name_fa)}
            onMouseLeave={handleMouseOutList}
            className={`p-4 cursor-pointer duration-200 font-body-large ${
              hoveredCountry === country.name_fa
                ? "bg-[#d4b7ff] hover:bg-[#d4b7ff]"
                : "hover:bg-[#d4b7ff]"
            }`}
          >
            {country.name_fa}
          </li>
        ))}
      </ul>
    </div>
  );
};

CountriesList.propTypes = {
  countries: PropTypes.arrayOf(
    PropTypes.shape({
      name_fa: PropTypes.string.isRequired,
    })
  ).isRequired,
  handleHoverList: PropTypes.func.isRequired,
  onCountryClick: PropTypes.func.isRequired,
  hoveredCountry: PropTypes.string,
  setHoveredCountry: PropTypes.func.isRequired,
  shouldAutoScroll: PropTypes.bool.isRequired,
  handleMouseOutList: PropTypes.func.isRequired,
  setZoomedCountry: PropTypes.func,
  zoomedCountry: PropTypes.string,
};

export default CountriesList;
