import { useEffect } from "react";
import { useReport360Store } from "store/report360Store.js";
import PropTypes from "prop-types";
import CardLoading from "components/ui/CardLoading.jsx";
import PlatformTabs from "./PlatformTabs.jsx";
import List from "./List.jsx";
import use360requestStore from "store/360requestStore.js";

const ProfileList = ({
  items = [],
  loading = false,
  total,
  page,
  setPage,
  handlePageChange,
  activeTab,
  setActiveTab,
  nextStep,
}) => {
  const handleItemSelect = (item) => {
    // setReport({ profile: item });
    // updateReport("content.source_info", item);
    // updateReport("content.report_platform", item.platform);
    const id = item?.user_name || item?.channel_username || item.source_name;
    nextStep(item.platform, id);
  };

  return (
    <>
      {loading ? (
        <div className="min-h-96 flex items-center text-center">
          <CardLoading />
        </div>
      ) : (
        items.all && (
          <div className="font-body-medium mx-5 h-full p-5 overflow-hidden">
            <PlatformTabs
              setPage={setPage}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
            />
            <List
              items={items[activeTab] || []}
              handleItemSelect={handleItemSelect}
              total={total}
              page={page}
              handlePageChange={handlePageChange}
            />
          </div>
        )
      )}
    </>
  );
};

ProfileList.propTypes = {
  items: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  handlePageChange: PropTypes.func.isRequired,
  setPage: PropTypes.func,
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
  nextStep: PropTypes.func.isRequired,
  total: PropTypes.number,
  page: PropTypes.number,
};

export default ProfileList;
