import { useReport360Store } from "store/report360Store";
import Age from "../components/Age";
import ContentDistribution from "../components/ContentDistribution";
import Excitement from "../components/Excitement";
import Gender from "../components/Gender";
import MapCharts from "../components/MapCharts";
import RepeatHashtags from "../components/RepeatHashtags";
import RepeatWords from "../components/RepeatWords";
import Sentiment from "../components/Sentiment";
import InterestCategories from "../components/InterestCategories";

const ContactsTab = () => {
  const { profile } = useReport360Store((state) => state.report);
  const platform = profile.platform;

  return (
    <div className="flex gap-3 min-h-screen">
      <div className="w-[60%] flex flex-col gap-3">
        {(platform === "twitter" || platform === "instagram") && (
          <div className="flex-1 !z-0">
            <MapCharts />
          </div>
        )}
        <div className="flex flex-col gap-3 flex-1">
          <div className="flex gap-3 flex-1">
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Sentiment />
              </div>
            </div>
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Excitement />
              </div>
            </div>
          </div>
          <div className="flex gap-3 flex-1">
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Age />
              </div>
            </div>
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Gender />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-[40%] flex flex-col gap-3">
        <div className="flex-1">
          <ContentDistribution />
        </div>
        <div className="flex-1">
          <RepeatWords />
        </div>
        <div className="flex-1">
          <RepeatHashtags />
        </div>
        <div className="flex-1">
          <InterestCategories />
        </div>
      </div>
    </div>
  );
};

export default ContactsTab;
