import PropTypes from "prop-types";
import IranMap from "./components/map";
import LocationSearchBar from "./components/SearchBar";
import { useState } from "react";
import WorldMap from "./components/WorldMap";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import { CTabs } from "components/ui/CTabs";
import { Card } from "components/ui/Card";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { ToastContainer } from "react-toastify";
import { useNavigate } from "react-router-dom";

const Step2Location = () => {
  const navigate = useNavigate();

  const [clickedCityName, setClickedCityName] = useState();
  const [mapActiveTab, setMapActiveTab] = useState("iran");
  const onMapTabChange = (name) => {
    setMapActiveTab(name);
  };
  const breadcrumbList = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "استعلام مکان محور" },
  ];
  useBreadcrumb(breadcrumbList);

  const nextStep = (q) => {
    navigate(`/app/report-360/report/entity/${q}/`);
  };

  return (
    <>
      <div className="h-full w-full">
        <LocationSearchBar
          clickedCityName={clickedCityName}
          nextStep={nextStep}
        />
        <Card className="mx-5 mt-3 w-[97%] card-animation card-delay">
          <div className="w-full flex flex-col gap-10">
            <div className="flex flex-row gap-2 w-full justify-between">
              <Title title="انتخاب مکان از روی نقشه" />
              <div className="flex ml-8">
                <CTabs
                  tabArray={[
                    { id: "iran", title: "ایران" },
                    { id: "world", title: "جهان" },
                  ]}
                  activeTab={mapActiveTab}
                  onChange={onMapTabChange}
                />
              </div>
            </div>
            <div className="flex w-full gap-4 px-3 items-center">
              <div className="w-full">
                {mapActiveTab === "iran" && (
                  <IranMap
                    onCityName={(cityName) => setClickedCityName(cityName)}
                  />
                )}
                {mapActiveTab === "world" && (
                  <WorldMap
                    onCountryClick={(cityName) => setClickedCityName(cityName)}
                    setMapActiveTab={setMapActiveTab}
                  />
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>
      <ToastContainer />
    </>
  );
};

Step2Location.propTypes = {
  nextStep: PropTypes.func.isRequired,
};
export default Step2Location;
