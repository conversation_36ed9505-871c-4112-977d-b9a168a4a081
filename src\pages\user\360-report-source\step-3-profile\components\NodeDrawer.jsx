import { useState } from "react";
import { UsersThree } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import Drawer from "components/Drawer";
import Divider from "components/ui/Divider";
import PostType from "./PostType";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import user from "assets/images/default.png";
import PostTypeStats from "./PostTypeStats";

const NodeDrawer = ({
  influence,
  setShowMore,
  nodeInfo,
  totalCounts,
  badges,
  quote,
}) => {
  const [activatedFilter, setActivatedFilter] = useState();
  return (
    <Drawer setShowMore={setShowMore}>
      <div className="flex justify-between items-center px-6 pb-4">
        <div className="flex flex-col justify-center mx-auto items-center gap-2">
          <img
            src={
              influence
                ? nodeInfo[0]?.avatar || user
                : nodeInfo[0]?.centralNodeAvatar
            }
            alt="node"
            className="rounded-full w-20 h-20"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = user;
            }}
          />
          <div className="flex flex-col justify-center mx-auto items-center">
            <div className="font-subtitle-large max-w-[150px] truncate overflow-hidden whitespace-nowrap">
              {influence
                ? nodeInfo[0]?.userTitle
                : nodeInfo[0]?.centralNodeTitle}
            </div>
            <span className="text-[#878787] font-overline-medium">
              {influence
                ? nodeInfo[0]?.userName
                : nodeInfo[0]?.centralNodeUsername}
              @
            </span>
            <div className="flex py-1 items-center gap-2 font-body-small">
              {influence
                ? toPersianNumber(formatShortNumber(nodeInfo[0]?.followerCount))
                : toPersianNumber(
                    formatShortNumber(nodeInfo[0]?.centralNodeFollower)
                  )}
              <UsersThree size={15} className="text-[#878787]" />
            </div>
          </div>
        </div>

        <div className="flex flex-col items-center justify-center mx-7">
          <span className="font-subtitle-large">
            {toPersianNumber(totalCounts)}
          </span>
          <div className="flex items-center">
            <div className="w-32 h-1 bg-black"></div>
            <div
              className="w-0 h-5 border-t-8 border-b-8 border-r-8 border-r-black border-transparent"
              style={{
                borderTopWidth: 11,
                borderBottomWidth: 11,
                borderRightWidth: 11,
              }}
            ></div>
          </div>
        </div>

        <div className="flex flex-col justify-center mx-auto items-center gap-1">
          <img
            src={
              influence
                ? nodeInfo[0]?.centralNodeAvatar
                : nodeInfo[0]?.avatar || user
            }
            alt="node"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = user;
            }}
            className="rounded-full w-20 h-20"
          />
          <div className="flex flex-col justify-center mx-auto items-center">
            <div className="font-subtitle-large  max-w-[150px] truncate overflow-hidden whitespace-nowrap">
              {influence
                ? nodeInfo[0]?.centralNodeTitle
                : nodeInfo[0]?.userTitle}
            </div>
            <span className="text-[#878787] font-overline-medium">
              {influence
                ? nodeInfo[0]?.centralNodeUsername
                : nodeInfo[0]?.userName}
              @
            </span>
            <div className="flex py-1 items-center gap-2 font-body-small">
              {influence
                ? toPersianNumber(
                    formatShortNumber(nodeInfo[0]?.centralNodeFollower)
                  )
                : toPersianNumber(
                    formatShortNumber(nodeInfo[0]?.followerCount)
                  )}
              <UsersThree size={15} className="text-[#878787]" />
            </div>
          </div>
        </div>
      </div>
      <Divider />
      <PostTypeStats
        badges={badges}
        onBadgeSelect={(badge) => setActivatedFilter(badge)}
      />
      <Divider />
      <div className="pt-5">
        <PostType
          quote={quote}
          influence={influence}
          activatedFilter={activatedFilter}
        />
      </div>
    </Drawer>
  );
};
NodeDrawer.propTypes = {
  setShowMore: PropTypes.func.isRequired,
  nodeInfo: PropTypes.arrayOf(
    PropTypes.shape({
      avatar: PropTypes.string,
      originalAvatar: PropTypes.string,
      userTitle: PropTypes.string,
      userName: PropTypes.string,
      followerCount: PropTypes.number,
      centralNodeAvatar: PropTypes.string,
      centralNodeTitle: PropTypes.string,
      centralNodeUsername: PropTypes.string,
      centralNodeFollower: PropTypes.string,
    })
  ).isRequired,
  totalCounts: PropTypes.number.isRequired,
  badges: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.node,
      name: PropTypes.string,
      count: PropTypes.number,
    })
  ).isRequired,
  quote: PropTypes.string.isRequired,
  influence: PropTypes.bool.isRequired,
};

export default NodeDrawer;
