import { toPersianNumber } from "utils/helper";
import ToolTip from "components/ui/ToolTip";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const ProgressBar = ({ data, mode = "line" }) => {
  if (data?.length == 0) {
    return (
      <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium">
        موردی یافت نشد
      </p>
    );
  }

  if (mode === "pie") {
    // Transform data for Highcharts pie chart
    const chartData =
      data?.map((item) => ({
        name: item.tooltip, // Use tooltip as the name for the pie slice
        y: parseFloat(item.percentage), // Convert percentage string to number
        color: item.color, // Keep the color
      })) || [];

    const options = {
      chart: {
        type: "pie",
        margin: [0, 0, 0, 0],
        padding: [0, 0, 0, 0],
        height: 220,
        width: 350,
      },
      title: {
        text: null,
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        useHTML: true,
        formatter: function () {
          return `<div dir="rtl" style='direction:rtl; font-family:iranyekan;'>
              <b>${this.key}</b>
              <p>${toPersianNumber(this.percentage.toFixed(0))}٪</p>
            </div>`;
        },
      },
      subtitle: {
        text: null,
      },
      plotOptions: {
        series: {
          size: "60%",
          allowPointSelect: true,
          cursor: "pointer",
          dataLabels: [
            {
              enabled: true,
              distance: 15,
              format: "{point.name}",
            },
            {
              enabled: false,
              distance: -40,
              format: "{point.percentage:.1f}%",
              style: {
                fontSize: "1.2em",
                textOutline: "none",
                opacity: 0.7,
              },
              filter: {
                operator: ">",
                property: "percentage",
                value: 10,
              },
            },
          ],
        },
      },
      series: [
        {
          name: "Percentage",
          colorByPoint: true,
          data: chartData,
        },
      ],
    };

    const value = data?.find(
      (item) => item.tooltip === "میزان محتوای ناسالم"
    )?.percentage;

    return (
      <div className="flex flex-row items-center w-full h-[225px]">
        <div className="flex-1">
          <p
            className={`font-body-large font-bold text-light-neutral-text-medium`}
          >
            محتوای توهین‌آمیز
          </p>
          <p className="font-headline-large text-light-neutral-text-low">
            {toPersianNumber(value)}٪
          </p>

          {/* <p className={`font-headline-medium leading-10`}>{data[0].tooltip}</p>
          <p className="font-headline-large text-light-neutral-text-low">
            {toPersianNumber(data[0].percentage)}٪
          </p> */}
        </div>
        <HighchartsReact highcharts={Highcharts} options={options} />
      </div>
    );
  }
  return (
    <>
      <div className="flex flex-col items-center w-full">
        {/* Percentage Labels */}
        <div className="flex justify-between w-full font-semibold mb-1">
          {data?.map((item) => (
            <span
              key={item?.id}
              className="font-body-small cursor-pointer"
              style={{ color: item?.color }}
            >
              <ToolTip comp={toPersianNumber(item?.tooltip)}>
                {toPersianNumber(item?.percentage)}%
              </ToolTip>
            </span>
          ))}
        </div>
        {/* Progress Bar */}
        <div className="w-full h-3 rounded-full bg-gray-200 flex overflow-hidden">
          {data?.map((item, index) => (
            <div
              key={item?.id}
              className={`h-full first:rounded-l-full last:rounded-r-full`}
              style={{
                width: `${item?.percentage}%`,
                backgroundColor: item?.color,
              }}
            >
              {/* <ToolTip comp={item?.title} /> */}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProgressBar;
