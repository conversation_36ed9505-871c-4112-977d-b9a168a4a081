import { memo, useCallback } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { sentiment } from "utils/selectIcon";
import { Card } from "components/ui/Card";
import PropTypes from "prop-types";
import "../../style.css";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const PersonSentiment = ({ activePlatform }) => {
  const sourceReport = use360requestStore((state) => ({
    sentiments: state.report?.content?.report_info?.sentiments,
  }));

  const processResult = useCallback((data) => {
    let result = [
      { name: "مثبت", y: 0, count: 0 },
      { name: "خنثی", y: 0, count: 0 },
      { name: "منف<PERSON>", y: 0, count: 0 },
    ];

    const total = data
      .map(({ count }) => count)
      .reduce((acc, curr) => acc + curr, 0);

    data?.forEach(({ key, count }) => {
      if (key === "negative") {
        result[2].count = count || 0;
        result[2].y = parseFloat(((count / total) * 100).toFixed(2)) || 0;
      } else if (key === "neutral") {
        result[1].count = count || 0;
        result[1].y = parseFloat(((count / total) * 100).toFixed(2)) || 0;
      } else if (key === "positive") {
        result[0].count = count || 0;
        result[0].y = parseFloat(((count / total) * 100).toFixed(2)) || 0;
      }
    });

    return result;
  }, []);

  const data = sourceReport.sentiments?.[activePlatform] || [];
  const processedData = processResult(data);

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Sentiment",
      data: processedData.map(({ count }) => count) || [0, 0, 0],
      time: ["مثبت", "خنثی", "منفی"],
    },
  ];

  const time = ["مثبت", "خنثی", "منفی"];

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay h-full">
      <div className="flex items-center justify-between">
        <p className="font-subtitle-large text-right">تحلیل احساسات</p>
        <ExportMenu
          chartSelector=".person-sentiment-container"
          fileName="person-sentiment"
          series={series}
          time={time}
          excelHeaders={["Sentiment", "Count"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="تحلیل احساسات"
        />
      </div>
      <Divider />
      {processedData.length > 0 ? (
        <div className="flex person-sentiment-container flex-1 justify-center items-center">
          <Doughnut
            name="sentiment"
            height={240}
            data={processedData}
            legendFormatter={function () {
              return `<div dir="rtl" style="font-family: iranyekan; display: flex; gap: 30%; width: 100%; padding: 2px; align-items: center; margin-right: 2rem;">
                  <span style="font-size: 16px; justify-self: start;">
                    ${toPersianNumber(this.y?.toFixed(0))}%
                  </span>
                  <div style="display: flex; gap: 8px; align-items: center; padding-right: 1rem;">
                    <span style="color: ${this.color}; font-size: 14px;">
                      ${this.name}
                    </span> 
                    <img src=${
                      sentiment[this.name]
                    } style="width: 20px; height: 20px; margin-right: 15px;" />
                  </div>
                </div>`;
            }}
            tooltipFormatter={function () {
              return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan">
                <div>${this.key}</div>
                <div>${toPersianNumber(
                  formatShortNumber(this.point.count)
                )}</div>
              </div>`;
            }}
            colors={["#1CB0A5", "#00000080", "#E0526A"]}
          />
        </div>
      ) : (
        <div className="h-[240px] flex items-center justify-center font-subtitle-medium">
          داده‌ای برای نمایش وجود ندارد
        </div>
      )}
    </Card>
  );
};

PersonSentiment.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default memo(PersonSentiment);
