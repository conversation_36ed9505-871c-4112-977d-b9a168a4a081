import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eoJSO<PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import markerIcon from "leaflet/dist/images/marker-icon.png";
import markerIcon2x from "leaflet/dist/images/marker-icon-2x.png";
import markerShadow from "leaflet/dist/images/marker-shadow.png";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import provinceGeoJson from "../../../../../assets/geojson/iran_provinces.json";
import cityJson from "../../../../../assets/geojson/iran_cities.json";

const IranMap = () => {
  const [provinceData, setProvinceData] = useState(null);
  const [cityGeoJson, setCityGeoJson] = useState(null);
  const [selectedProvince, setSelectedProvince] = useState(null);

  useEffect(() => {
    setProvinceData(provinceGeoJson);

    // Convert city JSON to GeoJSON
    const cityFeatures = cityJson.map((city) => ({
      type: "Feature",
      properties: { name: city.name, province: city.province },
      geometry: {
        type: "Point",
        coordinates: [parseFloat(city.longitude), parseFloat(city.latitude)],
      },
    }));
    setCityGeoJson({ type: "FeatureCollection", features: cityFeatures });

    delete L.Icon.Default.prototype._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconUrl: markerIcon,
      iconRetinaUrl: markerIcon2x,
      shadowUrl: markerShadow,
    });
  }, []);

  const bounds = [
    [24.0, 44.0],
    [40.5, 63.5],
  ];

  const provinceStyle = {
    color: "purple",
    weight: 2,
    opacity: 1,
    fillColor: "gray",
    fillOpacity: 0.1,
  };

  const cityStyle = {
    color: "#432fa7",
    weight: 1,
    opacity: 1,
    fillColor: "#432fa7",
    fillOpacity: 0.5,
  };

  const highlightFeature = (e) => {
    const layer = e.target;
    layer.setStyle({
      weight: 3,
      color: "yellow",
      fillOpacity: 0.5,
    });
  };

  const resetHighlight = (e) => {
    e.target.setStyle({
      weight: 2,
      color: "purple",
      fillOpacity: 0.1,
    });
  };

  const onEachProvinceFeature = (feature, layer) => {
    layer.on({
      mouseover: highlightFeature,
      mouseout: resetHighlight,
      click: () => {
        setSelectedProvince(feature.properties.name); 
      },
    });
    layer.bindTooltip(feature.properties.name, {
      permanent: false,
      direction: "top",
    });
  };

  const onEachCityFeature = (feature, layer) => {
    if (!selectedProvince || feature.properties.province === selectedProvince) {
      layer.setStyle(cityStyle); 
      layer.bindTooltip(feature.properties.name, {
        permanent: false,
        direction: "top",
      });
    } else {
      layer.setStyle({ opacity: 0 }); 
    }
  };

  return (
    <Card classname="">
      <div className="flex !flex-col !w-full">
        <div>
          <Title title={"مکان"}></Title>
        </div>
        <br />
        <MapContainer
          center={[32.0, 53.6]}
          zoom={6}
          style={{ width: "100%", height: "500px" }}
          maxBounds={bounds}
          maxBoundsViscosity={1.0}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
          {provinceData && (
            <GeoJSON
              data={provinceData}
              style={provinceStyle}
              onEachFeature={onEachProvinceFeature}
            />
          )}
          {cityGeoJson && (
            <GeoJSON
              data={cityGeoJson}
              pointToLayer={(feature, latlng) =>
                L.circleMarker(latlng, {
                  radius: 5,
                  fillColor: "green",
                  color: "darkgreen",
                  weight: 1,
                  opacity: 1,
                  fillOpacity: 0.8,
                })
              }
              onEachFeature={onEachCityFeature}
            />
          )}
        </MapContainer>
      </div>
    </Card>
  );
};

export default IranMap;
