import { memo, useEffect, useState } from "react";
import DatePicker from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_fa from "react-date-object/locales/persian_fa";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import TimePicker from "react-multi-date-picker/plugins/time_picker";
import DatePanel from "react-multi-date-picker/plugins/date_panel";

const RangeDatePicker = ({ from, to, onChange = () => {} }) => {
  const oneDayInMillis = 24 * 60 * 60 * 1000;
  const value = {
    from:
      from ||
      new Date(new Date().getTime() - oneDayInMillis).setHours(0, 0, 0, 0),
    to: to || new Date(),
  };
  const [fromValue, setFromValue] = useState(new Date(value?.from));
  const [toValue, setToValue] = useState(new Date(value?.to));
  const [values, setValues] = useState([fromValue, toValue]);

  const handleDateChange = (e) => {
    if (e.length < 2) return;
    setFromValue(new Date(new Date(e[0]).setSeconds(0, 0)));
    setToValue(new Date(new Date(e[1]).setSeconds(59, 0)));
    onChange({
      from: new Date(new Date(e[0]).setSeconds(0, 0)),
      to: new Date(new Date(e[1]).setSeconds(59, 0)),
    });
  };

  // useEffect(() => {
  //   onChange({ ...value });
  // }, []);

  return (
    <DatePicker
      className={"font-body-small"}
      value={values}
      calendar={persian}
      locale={persian_fa}
      calendarPosition="bottom-right"
      range
      portal
      showOtherDays
      rangeHover
      containerClassName={"datepicker-container"}
      onChange={handleDateChange}
      dateSeparator="      تا      "
      format="HH:mm - YYYY/MM/DD"
      renderButton={(direction, handleClick) => (
        <button onClick={handleClick}>
          {direction === "right" ? (
            <CaretLeft size={20} />
          ) : (
            <CaretRight size={20} />
          )}
        </button>
      )}
      plugins={[
        <TimePicker key="timepicker" position="bottom" hideSeconds />,
        <DatePanel key="datePanel" position="right" />,
      ]}
      style={{
        fontSize: "14px",
        // direction: "ltr",
      }}
    />
  );
};

RangeDatePicker.propTypes = {
  from: PropTypes.instanceOf(Date),
  to: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
};

export default memo(RangeDatePicker);
