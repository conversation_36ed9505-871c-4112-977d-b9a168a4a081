import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import useSearchStore from "store/searchStore.js";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { fetchBulletinTemplates } from "./utils.js";

import PreviewSection from "./components/PreviewSection.jsx";
import CoverSettingSection from "./components/CoverSettingSection.jsx";
import HeaderSettingSection from "./components/HeaderSettingSection.jsx";
import IntroSettingSection from "./components/IntroSettingSection.jsx";
import ListSettingSection from "./components/ListSettingSection.jsx";
import { ToastContainer } from "react-toastify";
import { useBulletinStore } from "store/bulletinStore.js";
import { Card } from "components/ui/Card.jsx";
import template from "../../../../assets/images/bulletin-template.png";
import templateHd from "../../../../assets/images/bulletin-hd.png";
import {
  Check,
  Eye,
  FilePlus,
  PencilSimpleLine,
  TrashSimple,
  X,
} from "@phosphor-icons/react";
import Drawer from "components/Drawer/index.jsx";
import PopUp from "components/ui/PopUp.jsx";
import { CInput } from "components/ui/CInput.jsx";
import { CButton } from "components/ui/CButton.jsx";

const Step4 = ({
  isTemplateSelected,
  onTemplateSelected,
  setIsTemplateSelected,
  showPopup,
  setShowPopup,
}) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata, title, step } = useBulletinStore((state) => state.bulletin);
  const { setShowFilterList, setShowSearchBox } = useSearchStore();

  const [selectedInput, setSelectedInput] = useState("");
  const [showDrawer, setShowDrawer] = useState(false);
  const [selectedCardIndex, setSelectedCardIndex] = useState(null);
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setShowFilterList(false);
    setShowSearchBox(false);
    const help = JSON.parse(JSON.stringify(metadata));
    help["cover_title"] = title;
    setBulletin({ metadata: { ...help } });
  }, []);

  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        const templatesData = await fetchBulletinTemplates();
        setTemplates(templatesData);
      } catch (error) {
        console.error("Error loading templates:", error);
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, []);

  useEffect(() => {
    onTemplateSelected(isTemplateSelected);
  }, [isTemplateSelected, onTemplateSelected]);

  const handleCardClick = (index) => {
    setSelectedCardIndex((prevIndex) => (prevIndex === index ? null : index));
  };

  return (
    <>
      {!isTemplateSelected ? (
        <Card className="w-full flex flex-col text-right gap-1">
          <h3 className="flex justify-end font-subtitle-large">انتخاب قالب</h3>
          <span className="font-body-small text-[#8f8f8f]">
            . برای بولتن یک قالب انتخاب کنید یا آن را ویرایش کنید. همچنین
            می‌توانید قالب‌های جدید بسازید
          </span>
          <div className="py-4">
            <Divider />
          </div>
          <div
            className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6"
            dir="rtl"
          >
            <div
              className="flex flex-col gap-4 justify-center border-2 border-dashed border-gray-300 rounded-lg p-4 items-center hover:cursor-pointer"
              onClick={() => setIsTemplateSelected(true)}
            >
              <div className="flex flex-col gap-3 justify-center w-full">
                <div className="flex justify-center">
                  <FilePlus size={80} fill="#8f8f8f" />
                </div>
                <div className="text-center text-gray-500 font-button-medium">
                  قالب جدید
                </div>
              </div>
            </div>

            {loading ? (
              <div className="col-span-full flex justify-center items-center py-8">
                <div className="text-gray-500">در حال بارگذاری قالب‌ها...</div>
              </div>
            ) : (
              templates.map((templateItem, i) => (
                <div
                  key={templateItem.id || i}
                  className={`relative flex flex-col gap-5 justify-center border rounded-lg p-4 items-center hover:cursor-pointer ${
                    selectedCardIndex === i
                      ? "!bg-[#B4ABE34D] border-[#4D36BF]"
                      : ""
                  }`}
                  onClick={() => handleCardClick(i)}
                >
                  <div className="absolute top-2 right-2">
                    <input
                      type="checkbox"
                      className="w-4 h-4"
                      checked={selectedCardIndex === i}
                      onChange={() => {}} // Prevent default checkbox behavior, controlled by card click
                    />
                  </div>
                  <div className="flex justify-center w-full">
                    <img
                      src={templateItem.preview_image || template}
                      alt="bulletin template"
                      className="w-[26%]"
                    />
                  </div>
                  <div className="font-body-medium font-bold text-center">
                    {templateItem.name || "نام قالب خاص که کاربر تعیین شده"}
                  </div>
                  <div className="flex gap-8 justify-center">
                    <div className="flex flex-col gap-2" dir="rtl">
                      <span
                        className={`${
                          templateItem.features?.cover_file
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.cover_file ? <Check /> : <X />}
                        فایل کاور
                      </span>
                      <span
                        className={`${
                          templateItem.features?.page_number
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.page_number ? <Check /> : <X />}
                        شماره صفحه
                      </span>
                      <span
                        className={`${
                          templateItem.features?.table_of_contents
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.table_of_contents ? (
                          <Check />
                        ) : (
                          <X />
                        )}
                        فهرست
                      </span>
                    </div>
                    <div className="flex flex-col gap-2" dir="rtl">
                      <span
                        className={`${
                          templateItem.features?.introduction
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.introduction ? (
                          <Check />
                        ) : (
                          <X />
                        )}
                        مقدمه
                      </span>
                      <span
                        className={`${
                          templateItem.features?.header
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.header ? <Check /> : <X />}
                        سربرگ
                      </span>
                      <span
                        className={`${
                          templateItem.features?.subtitle
                            ? "text-[#17968C]"
                            : "text-[#BE223C]"
                        } flex gap-1 items-center`}
                      >
                        {templateItem.features?.subtitle ? <Check /> : <X />}
                        عنوان فرعی
                      </span>
                    </div>
                  </div>
                  <Divider />
                  <div className="flex justify-center items-center gap-6">
                    <div className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer">
                      <TrashSimple size={16} />
                    </div>
                    <div
                      className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                      onClick={() => setIsTemplateSelected(true)}
                    >
                      <PencilSimpleLine size={16} />
                    </div>
                    <div
                      className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                      onClick={() => setShowDrawer(true)}
                    >
                      <Eye size={16} />
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </Card>
      ) : (
        <div className="p-6 bg-white rounded-lg [direction:rtl]">
          <div>
            <span className="font-body-medium">تنظیمات صفحات</span>
          </div>
          <div className="py-6">
            <Divider />
          </div>
          <div className="grid grid-cols-12 gap-6">
            <div className="divide-y col-span-8">
              <CoverSettingSection
                isOpen={true}
                selectedInput={selectedInput}
                setSelectedInput={setSelectedInput}
              />
              <HeaderSettingSection
                selectedInput={selectedInput}
                setSelectedInput={setSelectedInput}
              />
              <IntroSettingSection
                selectedInput={selectedInput}
                setSelectedInput={setSelectedInput}
              />
              <ListSettingSection />
            </div>
            <PreviewSection />
          </div>

          <ToastContainer />
        </div>
      )}
      {showDrawer && (
        <Drawer setShowMore={setShowDrawer}>
          <div className="flex flex-col gap-6 pb-9">
            <div className="flex justify-center w-full">
              <img
                src={templateHd}
                alt="bulletin template"
                className="w-[40%]"
              />
            </div>
            <div className="flex justify-between items-center">
              <div
                className="flex flex-col gap-2 text-[#8f8f8f] font-body-medium"
                dir="right"
              >
                <span>آخرین استفاده از قالب</span>
                <span>تعداد استفاده از قالب</span>
                <span>تعداد استفاده با ویرایش</span>
              </div>
              <div className="flex flex-col gap-2 font-body-large text-left">
                <span>۱۴۰۲٫۰۲/۲۳ - ۱۲:۳۰</span>
                <span>۱۲</span>
                <span>۱۲</span>
              </div>
            </div>
            <Divider />
            <h6 className="font-subtitle-medium" dir="right">
              تنظیمات صفحه اصلی (جلد)
            </h6>
            <div className="flex justify-between items-center">
              <div
                className="flex flex-col gap-2 text-[#8f8f8f] font-body-medium h-full"
                dir="right"
              >
                <span>عنوان اصلی بولتن</span>
                <span>عنوان فرعی</span>
                <span>ایجاد کننده</span>
                <span>تاریخ ایجاد</span>
                <span>فایل کاور</span>
                <span>فایل لوگو</span>
              </div>
              <div className="flex flex-col gap-2 font-body-large text-left h-full">
                <span>بولتن انتخابات</span>
                <span>بولتن انتخابات</span>
                <span>بولتن انتخابات</span>
                <span>بولتن انتخابات</span>
                <span>بولتن انتخابات</span>
                <span>بولتن انتخابات</span>
              </div>
            </div>
            <Divider />
            <h6 className="font-subtitle-medium" dir="right">
              تنظیمات صفحه اصلی (جلد)
            </h6>
            <div className="flex justify-between items-center">
              <div
                className="flex flex-col gap-2 text-[#8f8f8f] font-body-medium h-full"
                dir="right"
              >
                <span>متن سربرگ</span>
                <span>تاریخ</span>
                <span>شماره صفحه</span>
                <span>فایل لوگو</span>
              </div>
              <div className="flex flex-col gap-2 font-body-large text-left h-full">
                <span>ثبت نشده</span>
                <span>ثبت نشده</span>
                <span>ثبت نشده</span>
                <span>ثبت نشده</span>
              </div>
            </div>
            <Divider />
            <h6 className="font-subtitle-medium" dir="right">
              تنظیمات صفحه مقدمه
            </h6>
            <div className="flex justify-between items-center">
              <div
                className="flex flex-col gap-2 text-[#8f8f8f] font-body-medium h-full"
                dir="right"
              >
                <span>عنوان مقدمه</span>
                <span>متن مقدمه</span>
              </div>
              <div className="flex flex-col gap-2 font-body-large text-left h-full">
                <span>مقدمه این بولتن</span>
                <span className="w-full truncate">متن بلند مقدمه...</span>
              </div>
            </div>
            <Divider />
            <h6 className="font-subtitle-medium" dir="right">
              تنظیمات فهرست
            </h6>
            <div className="flex justify-between items-start">
              <div
                className="flex flex-col gap-3 text-[#8f8f8f] font-body-medium h-full"
                dir="right"
              >
                <span>نمایش فهرست</span>
                <span>ترتیب فهرست:</span>
              </div>
              <div className="flex flex-col gap-3 font-body-large text-left h-full">
                <span className="text-green-600">نمایش داده شود</span>
                <span></span>
              </div>
            </div>
          </div>
        </Drawer>
      )}
      {showPopup && (
        <PopUp
          isOpen={showPopup}
          onClose={() => setShowPopup(false)}
          title={"اطلاعات اختصاصی هر بولتن"}
          hasCloseIcon={true}
          width="560px"
          hasButton={false}
        >
          <div className="pt-5">
            <CInput
              id={"q"}
              name={"q"}
              title="عنوان اصلی بولتن (نمایش در فایل)"
              inset={true}
              // headingIcon={<MagnifyingGlass />}
              size={"md"}
              validation={"none"}
              // inputProps={{ onKeyDown: handleSearch }}
              direction={"rtl"}
              placeholder={"برای فایل گزارش بولتن یک عنوان اصلی بنویسید"}
              // onChange={(e) => setSearchValue(e.target.value)}
              className={"flex-1 !mb-0"}
            />
            <p className="font-overline-medium text-right text-[#8f8f8f] pb-6">
              .این عنوان براساس عنوانی که در اینجا تعیین کردید انتخاب شده است
              اما می‌توانید آن را تغییر دهید
            </p>
            <CInput
              id={"q"}
              name={"q"}
              title="عنوان فرعی بولتن (اختیاری)"
              inset={true}
              // headingIcon={<MagnifyingGlass />}
              size={"md"}
              validation={"none"}
              // inputProps={{ onKeyDown: handleSearch }}
              direction={"rtl"}
              placeholder={"برای فایل گزارش بولتن یک عنوان فرعی بنویسید"}
              // onChange={(e) => setSearchValue(e.target.value)}
              className={"flex-1 !mb-0"}
            />
          </div>

          <div className="w-full flex items-center gap-7 pt-8">
            <CButton
              role={"primary"}
              mode={"fill"}
              onClick={() => setBulletin({ step: step + 1 })}
            >
              ذخیره و ادامه
            </CButton>
            <CButton
              size="md"
              role="neutral"
              onClick={() => setShowPopup(false)}
            >
              انصراف
            </CButton>
          </div>
        </PopUp>
      )}
    </>
  );
};

export default Step4;
