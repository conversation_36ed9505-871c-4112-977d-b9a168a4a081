import PropTypes from "prop-types";
import { useState } from "react";
import { toPersianNumber } from "utils/helper";

const PostTypeStats = ({ badges: post_type_badges, onBadgeSelect }) => {
  const [activeBadge, setActiveBadge] = useState(null);

  const handleBadgeClick = (badge) => {
    if (activeBadge === badge.engName) {
      setActiveBadge(null);
      onBadgeSelect(null);
    } else {
      setActiveBadge(badge.engName);
      onBadgeSelect(badge.engName);
    }
  };

  return (
    <div className="flex w-full gap-7 py-5">
      {post_type_badges.map((badge, i) => (
        <div
          key={i}
          className={`border rounded-md w-full flex gap-3 items-center justify-evenly cursor-pointer transition-colors ${
            activeBadge === badge.engName
              ? "!bg-[#B4ABE34D] border-[#4D36BF]"
              : "bg-[#f7f9fb]"
          }`}
          onClick={() => handleBadgeClick(badge)}
        >
          <div className="flex items-center gap-2 font-body-medium">
            <span
              className="p-1 rounded-lg"
              style={{ backgroundColor: badge.bgColor }}
            >
              {badge.icon}
            </span>
            {badge.name}
          </div>
          <div className="font-headline-large">
            {toPersianNumber(badge.count)}
          </div>
        </div>
      ))}
    </div>
  );
};

PostTypeStats.propTypes = {
  badges: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.node.isRequired,
      name: PropTypes.string.isRequired,
      count: PropTypes.number.isRequired,
      engName: PropTypes.string.isRequired,
    })
  ).isRequired,
  onBadgeSelect: PropTypes.func.isRequired,
};

export default PostTypeStats;
