import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { shortener, toPersianNumber } from "utils/helper.js";

const ReleaseChart360 = ({ series }) => {
  const trackColors = ["#7D6CD5"];

  const seriesWithColors = series.map((s, index) => {
    const dataWithTime = s.data.map((value, i) => ({
      y: value,
      x: new Date(s.time[i]).getTime(),
    }));

    return {
      ...s,
      data: dataWithTime,
      color: trackColors[index % trackColors.length],
      name: "محتوا",
    };
  });

  const chartOptions = {
    chart: {
      type: "spline",
      height: "470px",
      scrollablePlotArea: {
        minWidth: 600,
        scrollPositionX: 1,
      },
    },

    title: {
      text: null,
      align: "right",
      enabled: false,
    },
    subtitle: {
      text: null,
      align: "left",
    },
    xAxis: {
      type: "datetime",
      visible: true,
      labels: {
        formatter: function () {
          return new Date(this.value)
            .toLocaleDateString("fa-IR", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            })
            .replace(/[\u200E]/g, "");
        },
      },
      tickPixelInterval: 65,
    },
    legend: {
      enabled: false,
    },
    yAxis: {
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      minorGridLineWidth: 0,
      gridLineWidth: 1,
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      textAlign: "center",
      useHTML: true,
      shadow: false,
      crosshairs: true,
      shared: true,
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan';font-size: 13px;">`;

        const point = this.points[0];

        tooltipHTML += `<div style="display: flex; align-items: center; margin-bottom: 5px;">
              <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                point.color
              }; margin-right: 8px;"></div>
              <span>${shortener(point.series.name, 14, "rtl")}</span> 
              <span style="color: #333; margin-left: 5px;">${toPersianNumber(
                point.y
              )}</span>
              </div>`;

        const firstPointDate = new Date(point.x);
        const formattedFirstDate = firstPointDate
          .toLocaleDateString("fa-IR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .replace(/[\u200E]/g, "");

        tooltipHTML += `<div style="margin-top: 10px; text-align: center; color: #555;">${formattedFirstDate}</div>`;
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },

    credits: {
      enabled: false,
    },
    plotOptions: {
      spline: {
        lineWidth: 4,
        states: {
          hover: {
            lineWidth: 5,
          },
        },
        marker: {
          enabled: false,
        },
      },
    },
    series: seriesWithColors,
    navigation: {
      menuItemStyle: {
        fontSize: "10px",
      },
    },
  };

  return <HighchartsReact highcharts={Highcharts} options={chartOptions} />;
};

ReleaseChart360.propTypes = {
  series: PropTypes.array.isRequired,
};

export default ReleaseChart360;
