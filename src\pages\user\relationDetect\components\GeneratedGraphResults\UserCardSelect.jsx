import { UsersThree } from "@phosphor-icons/react";
import { shortener, toPersianNumber } from "utils/helper";
import defaultUserIcon from "src/assets/images/opinion-mining/user.png";

const UserCardSelect = ({
  avatar,
  isSelected,
  title,
  followers,
  accountId,
  handleSelect = () => {},
}) => {
  return (
    <div
      className={`select-none flex flex-col items-center justify-center border rounded-lg relative 
      ${
        isSelected
          ? "bg-light-primary-background-highlight"
          : "bg-light-neutral-background-low"
      } 
      w-28 h-36 cursor-pointer transition-all`}
      onClick={handleSelect}
    >
      <input
        type="checkbox"
        checked={isSelected}
        readOnly
        className="absolute top-1 right-1"
      />
      <div className="my-2">
        <img
          src={avatar ? avatar : defaultUserIcon}
          onError={(e) => (e.target.src = defaultUserIcon)}
          width={48}
          alt="user"
          className="rounded-full"
        />
      </div>
      <p className="font-subtitle-medium">{shortener(title, 11, "rtl")}</p>
      <p className="font-overline-small text-light-neutral-text-low">
        {shortener(accountId, 15, "rtl")}@
      </p>
      <div className="flex items-center gap-1">
        <span className="font-body-small">
          {toPersianNumber(followers?.toLocaleString())}
        </span>
        <UsersThree size={17} className="text-light-neutral-text-low" />
      </div>
    </div>
  );
};

export default UserCardSelect;
