import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import PieChart from "components/Charts/Pie";
import { toPersianNumber } from "utils/helper";
import { useEffect, useState } from "react";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const PoliticalSpectrum = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));

  const profile = sourceReport?.profile;

  // Initialize default tabs
  const tabs = [
    {
      id: "osoolgera",
      title: "حامیان انقلاب اسلامی",
    },
    { id: "barandaz", title: "برانداز" },
    { id: "saltanat", title: "سلطنت طلب" },
    { id: "monafegh", title: "منافق" },
    {
      id: "eslahtalab",
      title: "فعالین مدنی غرب‌گرا",
      count: 0,
    },
    {
      id: "edalatkhah",
      title: "عدالت‌خواه",
    },
    {
      id: "ahmadinezhad",
      title: "احمدی نژادی",
    },
  ];

  // Define all possible categories with their colors
  const allCategories = [
    { title: "نامشخص", color: "rgb(216, 216, 216)" },
    { title: "خاکستری", color: "rgba(128, 128, 128, 1)" },
    { title: "خودی", color: "rgba(0, 128, 0, 1)" },
    { title: "معاند", color: "rgba(255, 105, 180, 1)" },
  ];

  // Translator function to map API response values to Persian
  const translateCategory = (category) => {
    switch (category?.toLowerCase()) {
      case "opposite":
        return "معاند";
      case "ally":
      case "supporter":
        return "خودی";
      case "neutral":
      case "gray":
        return "خاکستری";
      case "unknown":
      case null:
      case undefined:
        return "نامشخص";
      default:
        return category || "نامشخص";
    }
  };
  // Map support_state to translated category
  const translatedTitle = translateCategory(
    sourceReport.profile?.political_category?.support_state || "unknown"
  );

  // Prepare thoughtLines with all categories, marking the active one
  const thoughtLine = allCategories.find(
    (category) => category.title === translatedTitle
  );

  const [chartData, setChartData] = useState(
    tabs.map((tab) => ({
      name: tab.title,
      y: tab.count,
    }))
  );
  const [activeTab, setActiveTab] = useState({ title: "هیچکدام", count: 0 });
  const [activeTabColor, setActiveTabColor] = useState("#000000");

  const colors = [
    "#007BFF",
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#96CEB4",
    "#FFEEAD",
    "#D4A5A5",
  ];

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Probability (%)",
      data: chartData.map((item) => item.y * 100),
      time: chartData.map((item) => item.name),
    },
  ];

  const time = chartData.map((item) => item.name);

  useEffect(() => {
    const labelCounts = profile?.political_category?.prob;
    const activeGroup = profile?.political_category?.label;

    if (labelCounts && activeGroup) {
      const updatedChartData = tabs.map((tab) => ({
        name: tab.title,
        y: parseFloat(labelCounts[tab.id]) || 0,
      }));

      setChartData(updatedChartData);

      const foundTab = tabs.find((tab) => tab.id === activeGroup) || {
        title: "هیچکدام",
        count: 0,
      };
      const activeTabIndex = tabs.findIndex((tab) => tab.id === activeGroup);
      setActiveTab({
        title: foundTab.title,
        count: labelCounts[activeGroup] || 0,
      });
      setActiveTabColor(
        activeTabIndex !== -1 ? colors[activeTabIndex] : "#000000"
      );
    }
  }, [date, profile?.political_category]);

  return (
    <Card className="h-full w-full">
      <div className="w-full">
        <div className="flex items-center justify-between px-3">
          <Title title="دسته‌بندی سیاسی" />
          <ExportMenu
            chartSelector=".political-spectrum-container"
            fileName="political-spectrum"
            series={series}
            time={time}
            excelHeaders={["Category", "Probability (%)"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            chartTitle="دسته‌بندی سیاسی"
          />
        </div>
        <div className="w-full political-spectrum-container px-4 flex flex-row gap-4">
          <div className="flex flex-1 flex-col justify-end items-start">
            <span className="px-6 font-body-medium">طیف سیاسی</span>
            <div className="flex px-6 gap-6 justify-center items-center">
              <span
                className="font-headline-large"
                style={{ color: activeTabColor }}
              >
                {activeTab.title}
              </span>
              <span className=" font-body-medium">
                {toPersianNumber((activeTab.count * 100).toFixed(0))}%
              </span>
            </div>
            <div className="flex w-full justify-start items-center px-6 border-t mt-8 pt-6 gap-3">
              <span>خط فکری:</span>
              <span
                className={`py-1 px-3 border rounded-md transition duration-300 hover:bg-[#EE9FAD]`}
                style={{
                  background: `linear-gradient(to left, ${thoughtLine.color} 0%, ${thoughtLine.color} 10%, rgba(255, 255, 255, 0) 100%)`,
                }}
              >
                {thoughtLine.title}
              </span>
            </div>
          </div>
          <div className="w-1/3">
            <PieChart
              height={160}
              data={chartData}
              showLabelsInTooltip={true}
              colors={colors}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PoliticalSpectrum;
