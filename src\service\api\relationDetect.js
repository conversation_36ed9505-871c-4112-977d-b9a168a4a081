import useFetch from "../index";

class RelationDetect {
  getGraphsList() {
    return useFetch.get(`/api/v1/graph/`);
  }
  generateGraph(data) {
    return useFetch.post(`/api/v1/graph/generate/`, data);
  }
  saveGraph(data) {
    return useFetch.post(`/api/v1/graph/`, data);
  }
  deleteGraph(id) {
    return useFetch.delete(`/api/v1/graph/${id}/`);
  }
  getGraph(id) {
    return useFetch.get(`/api/v1/graph/${id}/`);
  }
}
export default new RelationDetect();
