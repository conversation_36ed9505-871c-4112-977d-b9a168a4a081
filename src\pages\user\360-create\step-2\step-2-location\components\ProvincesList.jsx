import PropTypes from "prop-types";
import { CaretDown, CaretLeft } from "@phosphor-icons/react";
import { useState, useMemo, useRef, useEffect } from "react";
import cityJson from "../../../../../../assets/geojson/iran_cities.json";
import "../style.css";

const ProvincesList = ({
  setHoveredProvince,
  setZoomedProvince,
  onCityClick,
  onCityName,
  hoveredProvince,
  clickedCity,
  clickedCityHover,
  setClickedCityHover,
}) => {
  const [expandedProvinces, setExpandedProvinces] = useState({});
  const [activeProvince, setActiveProvince] = useState(null);
  const [isHoveringList, setIsHoveringList] = useState(false);
  const listRefs = useRef({});
  const cityRefs = useRef({});

  const provinceNameMap = {
    "East Azarbaijan": "آذربایجان شرقی",
    "West Azarbaijan": "آذربایجان غربی",
    Ardebil: "اردبیل",
    Esfahan: "اصفهان",
    Alborz: "البرز",
    Ilam: "ایلام",
    Bushehr: "بوشهر",
    Tehran: "تهران",
    "Chahar Mahall and Bakhtiari": "چهارمحال و بختیاری",
    "South Khorasan": "خراسان جنوبی",
    "Khorasan Razavi": "خراسان رضوی",
    "North Khorasan": "خراسان شمالی",
    Khuzestan: "خوزستان",
    Zanjan: "زنجان",
    Semnan: "سمنان",
    "Sistan and Baluchestan": "سیستان و بلوچستان",
    Fars: "شیراز",
    Qazvin: "قزوین",
    Qom: "قم",
    Kordestan: "کردستان",
    Kerman: "کرمان",
    "Kohgiluyeh and Buyer Ahmad": "کهگیلویه و بویراحمد",
    Gilan: "گیلان",
    Lorestan: "لرستان",
    Mazandaran: "مازندران",
    Markazi: "مرکزی",
    Hormozgan: "هرمزگان",
    Hamadan: "همدان",
    Yazd: "یزد",
  };

  const iranProvinces = Object.keys(provinceNameMap);

  const citiesByProvince = useMemo(() => {
    return cityJson.reduce((acc, city) => {
      const { province, city: cityName } = city;
      if (!acc[province]) {
        acc[province] = [];
      }
      acc[province].push(cityName);
      return acc;
    }, {});
  }, []);

  const handleProvinceClick = (province) => {
    setZoomedProvince(province);
    setExpandedProvinces((prev) => {
      const newExpandedProvinces = Object.keys(prev).reduce((acc, key) => {
        acc[key] = false;
        return acc;
      }, {});
      newExpandedProvinces[province] = !prev[province];
      return newExpandedProvinces;
    });
    setActiveProvince(province);
  };

  useEffect(() => {
    if (
      hoveredProvince &&
      listRefs.current[hoveredProvince] &&
      !isHoveringList
    ) {
      // setTimeout(() => {
      //   listRefs.current[hoveredProvince].scrollIntoView({
      //     behavior: "smooth",
      //     block: "center",
      //   });
      // }, 100);
    }
  }, [hoveredProvince, isHoveringList]);

  useEffect(() => {
    if (clickedCity) {
      const cityData = cityJson.find((city) => city.city === clickedCity);
      if (cityData) {
        const { province } = cityData;
        setExpandedProvinces((prev) => ({
          ...prev,
          [province]: true,
        }));
        setActiveProvince(province);

        setTimeout(() => {
          if (cityRefs.current[clickedCity]) {
            cityRefs.current[clickedCity].scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }, 100);
      }
    }
  }, [clickedCity]);

  useEffect(() => {
    if (clickedCity) {
      const provinceElement = document.querySelector(
        `#province-${clickedCity.province}`
      );
      const cityElement = document.querySelector(`#city-${clickedCity.name}`);

      if (provinceElement && cityElement) {
        provinceElement.classList.add("expanded");

        cityElement.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  }, [clickedCity]);

  return (
    <div className="flex">
      <div
        className="w-full border-gray-200 mapList-container overflow-y-auto"
        onMouseEnter={() => setIsHoveringList(true)}
        onMouseLeave={() => setIsHoveringList(false)}
      >
        <ul className="divide-y divide-gray-200 text-right">
          {iranProvinces.map((province, index) => (
            <li
              key={index}
              ref={(el) => (listRefs.current[province] = el)}
              className={`p-4 cursor-pointer ${
                activeProvince === province ? "bg-[#e9e6f7]" : ""
              } ${
                hoveredProvince === province
                  ? "bg-[#d4b7ff]"
                  : "hover:bg-[#e9e6f7]"
              } duration-200 font-body-large`}
              onMouseEnter={() => setHoveredProvince(province)}
              onMouseLeave={() => setHoveredProvince(null)}
              onClick={() => handleProvinceClick(province)}
            >
              <div
                className={`flex ${
                  activeProvince === province && expandedProvinces[province]
                    ? "font-bold"
                    : ""
                } items-center justify-between`}
              >
                <span
                  className={`${
                    activeProvince === province && expandedProvinces[province]
                      ? "border-b border-black pb-1"
                      : ""
                  }`}
                >
                  {provinceNameMap[province]}
                </span>
                <span className="ml-2">
                  {expandedProvinces[province] ? <CaretDown /> : <CaretLeft />}
                </span>
              </div>
              {expandedProvinces[province] && citiesByProvince[province] && (
                <ul className="ml-4 mt-2">
                  {citiesByProvince[province].map((city, index) => (
                    <li
                      key={index}
                      ref={(el) => (cityRefs.current[city] = el)}
                      className={`p-4 cursor-pointer hover:bg-[#e9e6f7] duration-200 transform ${
                        clickedCityHover & (clickedCity === city)
                          ? "bg-[#d4b7ff] font-bold"
                          : "hover:scale-105 hover:translate-x-[-2rem] hover:translate-y-1"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onCityName(city);
                        const cityData = cityJson.find(
                          (c) => c.city === city && c.province === province
                        );
                        if (cityData) {
                          onCityClick(cityData);
                        }
                      }}
                      onMouseEnter={() => setClickedCityHover(null)}
                    >
                      {city}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

ProvincesList.propTypes = {
  setHoveredProvince: PropTypes.func.isRequired,
  setZoomedProvince: PropTypes.func.isRequired,
  onCityClick: PropTypes.func.isRequired,
  onCityName: PropTypes.func.isRequired,
  hoveredProvince: PropTypes.string,
  clickedCity: PropTypes.string,
  clickedCityHover: PropTypes.bool,
  setClickedCityHover: PropTypes.func,
};

export default ProvincesList;
