import { Card } from "components/ui/Card";
import { useState, useEffect } from "react";
import "../../style.css";

const AboutPerson = () => {
  const [displayText, setDisplayText] = useState("");
  const fullText =
    "لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ، و با استفاده از طراحان گرافیک است، چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است، و برای شرایط فعلی تکنولوژی مورد نیاز، و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد، کتابهای زیادی در شصت و سه درصد گذشته حال و آینده، شناخت فراوان جامعه و متخصصان را می طلبد، تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی، و فرهنگ پیشرو در زبان فارسی ایجاد کرد، در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها، و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی، و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.";

  useEffect(() => {
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setDisplayText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
      }
    }, 20);

    return () => clearInterval(typingInterval);
  }, []);

  return (
    <Card className="flex flex-col gap-2 card-animation card-delay h-[26rem]">
      <h3 className="font-subtitle-large">درباره شخص</h3>
      <p className="font-body-small text-[#8f8f8f]">
        این محتوا بر اساس اطلاعات موجود از ویکی‌پدیا مربوط به شخص تهیه شده است
      </p>
      <p className="font-paragraph-large py-3">{displayText}</p>
    </Card>
  );
};

export default AboutPerson;
