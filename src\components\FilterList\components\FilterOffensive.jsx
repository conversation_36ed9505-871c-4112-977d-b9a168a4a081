import { ChatTeardropSlash } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { FilterCard } from "./FilterCard.jsx";
import { Checkbox } from "../../ui/Checkbox.jsx";
import useSearchStore from "store/searchStore.js";

export const FilterOffensive = ({ hasInfo = false, className = "" }) => {
  const {
    filters,
    setFilters,
    isFilterListOpen,
    toggleOpenClose,
    saveState,
    checkIsFilterActive,
  } = useSearchStore();

  const offensiveOptions = [
    { label: "توهین آمیز", id: "offensive", name: "offensive" },
    { label: "غیر توهین آمیز", id: "non-offensive", name: "non-offensive" },
  ];

  const handleChange = (selectedItem) => {
    const { id, isChecked } = selectedItem;
    let newItems = [];

    newItems = isChecked ? [id] : [];

    setFilters({ offensive: newItems });
    saveState();
  };

  return (
    <FilterCard
      onClick={!isFilterListOpen ? toggleOpenClose : undefined}
      icon={ChatTeardropSlash}
      hasInfo={hasInfo}
      hasBullet={checkIsFilterActive("offensive")}
      title={"محتوای توهین آمیز"}
      isOpen={isFilterListOpen}
      className={className}
    >
      <div
        className={
          isFilterListOpen ? "flex flex-row justify-between gap-4" : "hidden"
        }
      >
        {offensiveOptions.map((option) => (
          <Checkbox
            key={option.id}
            onChange={handleChange}
            label={option.label}
            id={option.id}
            name={option.name}
            checked={filters?.offensive?.includes(option?.name)}
            className={"flex-1 py-3"}
          />
        ))}
      </div>
    </FilterCard>
  );
};

FilterOffensive.propTypes = {
  hasInfo: PropTypes.bool,
  hasBullet: PropTypes.bool,
  className: PropTypes.string,
};
