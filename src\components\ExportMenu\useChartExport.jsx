import { useCallback } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import * as XLSX from "xlsx";

export const useChartExport = ({
  chartSelector,
  fileName,
  series,
  time,
  excelHeaders,
  onError,
  chartRef,
  useCanvasExport = false,
  customExcelFormat = null,
  data = null,
  chartTitle = "",
}) => {
  const exportChart = useCallback(
    async (format) => {
      try {
        if (format === "Excel") {
          let worksheetData = [];

          if (customExcelFormat === "radialCluster" && data?.trends) {
            const trendCategories = {
              Potential: "بالقوه",
              Increasing: "افزایشی",
              Decreasing: "کاهشی",
              Frequent: "فراگیر",
            };

            worksheetData = [
              ["نوع موج", "عنوان", "تاثیرگذاری (%)", "فراگیری", "زیرمجموعه‌ها"],
              ...data.trends.map((trend) => [
                trendCategories[trend.trend_type] || "نامشخص",
                trend.title,
                ((trend.stats.impact || 0) * 100).toFixed(0),
                trend.stats.posts || 0,
                trend.elements
                  .slice(0, 5)
                  .map((el) => el.content)
                  .join(", "),
              ]),
            ];
          } else {
            const uniqueTimes = [...new Set(time)].sort();
            worksheetData = [
              excelHeaders || ["Time", ...series.map((s) => s.name)],
              ...uniqueTimes.map((t) => [
                t,
                ...series.map((s) => {
                  const timeIndex = s.time.indexOf(t);
                  return timeIndex !== -1 ? s.data[timeIndex] : 0;
                }),
              ]),
            ];
          }

          const ws = XLSX.utils.aoa_to_sheet(worksheetData);
          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, "Chart Data");
          XLSX.writeFile(wb, `${fileName}.xlsx`);
        } else {
          const chartElement = document.querySelector(chartSelector);
          if (!chartElement) {
            throw new Error("Chart container not found");
          }

          const exportButton = chartElement.querySelector(
            ".export-menu-button"
          );
          if (exportButton) {
            exportButton.style.display = "none";
          }

          let canvas;
          if (useCanvasExport && format !== "PDF") {
            if (chartRef.current) {
              chartRef.current.pauseSimulation();
            }

            canvas = chartRef.current
              ? chartRef.current.getCanvas()
              : chartElement.querySelector("canvas");
            if (!canvas) {
              throw new Error("Canvas element not found");
            }

            const hasTitle = chartTitle && chartTitle.trim() !== "";
            const titleHeight = hasTitle ? 50 : 0;

            const tempCanvas = document.createElement("canvas");
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height + titleHeight;
            const ctx = tempCanvas.getContext("2d");

            ctx.fillStyle = "#ffffff";
            ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            if (hasTitle) {
              ctx.font = 'bold 20px "IranYekan", sans-serif';
              ctx.fillStyle = "#000000";
              ctx.textAlign = "center";
              ctx.textBaseline = "top";
              ctx.fillText(chartTitle, tempCanvas.width / 2, 10);
            }

            ctx.drawImage(canvas, 0, titleHeight);

            if (format === "PNG" || format === "JPEG") {
              try {
                const image = tempCanvas.toDataURL(
                  format === "PNG" ? "image/png" : "image/jpeg",
                  format === "JPEG" ? 0.9 : 1.0
                );
                const link = document.createElement("a");
                link.href = image;
                link.download = `${fileName}.${format.toLowerCase()}`;
                link.click();
              } catch (error) {
                throw new Error(
                  `Failed to generate ${format} image: ${error.message}`
                );
              }
            }

            if (chartRef.current) {
              chartRef.current.resumeSimulation();
            }
          } else {
            const html2CanvasOptions = {
              scale: 2,
              useCORS: true,
              allowTaint: false,
              backgroundColor: "#ffffff",
            };
            canvas = await html2canvas(chartElement, html2CanvasOptions);

            const hasTitle = chartTitle && chartTitle.trim() !== "";
            const titleHeight = hasTitle ? 100 : 0;

            const tempCanvas = document.createElement("canvas");
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height + titleHeight;
            const ctx = tempCanvas.getContext("2d");

            ctx.fillStyle = "#ffffff";
            ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            if (hasTitle) {
              ctx.font = 'bold 40px "IranYekan", sans-serif';
              ctx.fillStyle = "#000000";
              ctx.textAlign = "center";
              ctx.textBaseline = "top";
              ctx.fillText(chartTitle, tempCanvas.width / 2, 20);
            }

            ctx.drawImage(canvas, 0, titleHeight);

            if (format === "PNG") {
              const image = tempCanvas.toDataURL("image/png");
              const link = document.createElement("a");
              link.href = image;
              link.download = `${fileName}.png`;
              link.click();
            } else if (format === "JPEG") {
              const image = tempCanvas.toDataURL("image/jpeg", 0.9);
              const link = document.createElement("a");
              link.href = image;
              link.download = `${fileName}.jpeg`;
              link.click();
            } else if (format === "PDF") {
              const pdf = new jsPDF("p", "mm", "a4");
              const imgData = tempCanvas.toDataURL("image/png");
              const imgWidth = pdf.internal.pageSize.getWidth();
              const imgHeight =
                (tempCanvas.height * imgWidth) / tempCanvas.width;
              pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
              pdf.save(`${fileName}.pdf`);
            }
          }

          if (exportButton) {
            exportButton.style.display = "";
          }
        }
      } catch (error) {
        if (onError) {
          onError(error instanceof Error ? error : new Error("Unknown error"));
        } else {
          console.error(`Error exporting to ${format}:`, error);
        }
      }
    },
    [
      chartSelector,
      fileName,
      series,
      time,
      excelHeaders,
      onError,
      chartRef,
      useCanvasExport,
      customExcelFormat,
      data,
      chartTitle,
    ]
  );

  return { exportChart };
};
