import { useState, useEffect } from "react";
import Divider from "components/ui/Divider";
import RadialBar from "components/Charts/RadialBar";
import DropDown from "components/ui/DropDown";
import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import { parseNumber } from "utils/helper";
import use360requestStore from "store/360requestStore";
import { RESOURCE_SORT_TYPE } from "constants/sort-type.js";
import ExportMenu from "components/ExportMenu/index.jsx";
import { useNavigate } from "react-router-dom";
import Popup from "components/ui/PopUp";
import { UserSwitch } from "@phosphor-icons/react";

const PersonBestSource = ({ activePlatform }) => {
  const [sort, setSort] = useState({ fa: "محتوا", en: "date" });
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const navigate = useNavigate();
  const sourceReport = use360requestStore((state) => ({
    top_sources: state.report?.content?.report_info?.top_sources,
  }));

  const data = sourceReport.top_sources?.[activePlatform] || [];

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Source Count",
      data: data.map((item) => item.count),
      time: data.map((item) => item.title || item.key),
    },
  ];

  const time = data.map((item) => item.title || item.key);

  useEffect(() => {
    setSort({ fa: "محتوا", en: "date" });
  }, [activePlatform]);

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
    setSelectedItem(null);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };

  const handleProfileClick = (profile) => {
    try {
      navigate(
        `/app/report-360/report/source/${activePlatform}/${profile.key}`
      );
    } catch (e) {
      console.error("Profile click error:", e);
    }
  };

  return (
    <Card className="person-best-source-container flex flex-col h-full">
      <div className="flex items-center justify-between px-3">
        <div className="flex gap-5 items-center">
          <div className="font-subtitle-large">برترین منابع</div>
          <ExportMenu
            chartSelector=".person-best-source-container"
            fileName="person-best-source"
            series={series}
            time={time}
            excelHeaders={["Source", "Count"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
          />
        </div>
        <div className="flex flex-row items-center gap-4 [direction:rtl] relative z-30">
          <DropDown
            title="نمایش بر اساس"
            subsets={RESOURCE_SORT_TYPE[activePlatform]?.map((item) => item.fa)}
            selected={sort.fa}
            setSelected={(value) => {
              setSort(
                RESOURCE_SORT_TYPE[activePlatform]?.filter(
                  (item) => item.fa === value
                )[0]
              );
            }}
          />
        </div>
      </div>
      <div className="py-4">
        <Divider />
      </div>

      <div className="relative">
        <div className={`w-full grid grid-cols-12 gap-6 h-96`}>
          {data?.length === 0 ? (
            <div className="h-[270px] flex items-center justify-center font-subtitle-medium col-span-12">
              داده ای برای نمایش وجود ندارد
            </div>
          ) : (
            <>
              <div
                className={`col-span-${
                  activePlatform === "news" ? "6" : "7"
                } w-full flex flex-col gap-4 [direction:rtl]`}
              >
                <div
                  className={`grid ${
                    activePlatform === "news"
                      ? "grid-cols-[5fr_1fr_1fr_1fr]"
                      : "grid-cols-[5fr_1fr_1fr_1fr]"
                  } *:font-body-medium *:text-light-neutral-text-medium`}
                >
                  <div>منبع</div>
                  <div>{sort.fa}</div>
                </div>

                {data?.slice(0, 5).map((item) => (
                  <div
                    className={`grid ${
                      activePlatform === "news"
                        ? "grid-cols-[5fr_1fr_1fr_1fr]"
                        : "grid-cols-[5fr_1fr_1fr_1fr]"
                    } items-center p-1 cursor-pointer rounded-lg hover:bg-light-primary-background-highlight`}
                    key={item.id}
                    onClick={() => openConfirmPopup(item)}
                  >
                    <div className="flex gap-2 items-center">
                      <div
                        className="size-10 rounded-full"
                        style={{
                          backgroundImage: item?.avatar
                            ? `url(${item.avatar})`
                            : "url(/logo_small.png)",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          backgroundSize: "contain",
                        }}
                      ></div>
                      <div>
                        <div className="font-subtitle-medium">
                          {item?.title?.slice(0, 20) ||
                            item?.title ||
                            item?.key}
                        </div>
                        <div className="font-overline-medium">
                          {item.key &&
                            `${item.key} ${
                              activePlatform !== "news" ? "@" : ""
                            }`}
                        </div>
                      </div>
                    </div>
                    <div className="font-body-bold-medium">
                      {parseNumber(item?.count)}
                    </div>
                  </div>
                ))}
              </div>
              <div className="col-span-5">
                {!!data?.filter((item) => item.count).length && (
                  <RadialBar
                    data={data?.slice(0, 5).map((item) => ({
                      key: item.key,
                      count: item.count,
                      title: item.key || null,
                      y: item.count,
                    }))}
                  />
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های ۳۶۰ این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه بله به صفحه گزارشات ۳۶۰ این منبع
          منتقل خواهید شد.
        </p>
      </Popup>
    </Card>
  );
};

PersonBestSource.propTypes = {
  activePlatform: PropTypes.string.isRequired,
};

export default PersonBestSource;
