import PropTypes from "prop-types";
import { Card } from "components/ui/Card";

const PersonReportsInfo = ({ activeSpan }) => {
  const getTitle = () => {
    switch (activeSpan) {
      case "overview":
        return "نمای کلی";
      case "twitter":
        return "توئیتر";
      case "instagram":
        return "اینستاگرام";
      case "telegram":
        return "تلگرام";
      case "news":
        return "خبرگزاری";
      default:
        return "";
    }
  };

  const reportType = "موجودیت ";

  const getDescription = () => {
    switch (activeSpan) {
      case "overview":
        return (
          <>
            در این قسمت اطلاعات کلی در مورد این <strong>{reportType}</strong> را
            مشاهده می‌کنید
          </>
        );
      default:
        return (
          <>
            در این قسمت نتایج آماری در ارتباط با این
            <strong> {reportType}</strong> را مشاهده می‌کنید
          </>
        );
    }
  };

  return (
    <Card className="!z-[-10] flex justify-between items-center gap-2 border-r-8 border-[#4D36BF] card-delay my-3 !w-[98%] mx-3">
      <div className="flex flex-col gap-2">
        <h3 className="font-headline-small">{getTitle()}</h3>
        <p className="font-body-small text-[#8f8f8f]">{getDescription()}</p>
      </div>
    </Card>
  );
};

PersonReportsInfo.propTypes = {
  activeSpan: PropTypes.string.isRequired,
};

export default PersonReportsInfo;
