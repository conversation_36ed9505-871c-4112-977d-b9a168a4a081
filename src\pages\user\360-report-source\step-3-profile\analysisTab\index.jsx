import { useEffect, useState } from "react";
import Effectiveness from "../components/Effectiveness";
import Influence from "../components/Influence";
import PoliticalSpectrum from "../components/Political";
// import PoliticalChart from "../components/PoliticalChart";
// import RelatedGroups from "../components/RelatedGroups";
import SimilarSources from "../components/SimilarSources";
// import ThoughLineChart from "../components/ThoughLineChart";
import ThoughtLine from "../components/ThoughtLine";
import { memo } from "react";
import use360requestStore from "store/360requestStore";

const AnalysisTab = () => {
  const sourceReport = use360requestStore((state) => ({
    platform: state.report?.content?.report_platform,
  }));

  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: "smooth" });
  }, []);

  return (
    <>
      {sourceReport.platform !== "news" && (
        <div className="flex flex-col gap-3 h-full">
          <div className="flex gap-3 flex-1">
            <div className="w-full flex gap-3">
              {/* <div className="flex flex-col gap-3 w-[35%]">
                <div className="flex-1">
                </div>
                <div className="flex-1"></div>
                </div> */}
              {/* <div className="w-[16%]">
                <ThoughtLine />
              </div> */}
              <div className="w-[46%]">
                <PoliticalSpectrum />
              </div>
              <div className="w-[54%]">
                <SimilarSources />
              </div>
              {/* <div className="flex-1">
                <RelatedGroups />
              </div> */}
            </div>
            {/* <div className="w-[40%] flex flex-col gap-3">
              <div className="flex-1">
                <ThoughLineChart />
              </div>
              <div className="flex-1">
                <PoliticalChart />
              </div>
            </div> */}
          </div>
          {sourceReport.platform !== "telegram" && (
            <>
              {isFullScreen ? (
                <div className="flex flex-col gap-3">
                  <div className="flex-1">
                    <Effectiveness
                      isFullScreen={isFullScreen}
                      setIsFullScreen={setIsFullScreen}
                    />
                  </div>
                  <div className="flex-1">
                    <Influence
                      isFullScreen={isFullScreen}
                      setIsFullScreen={setIsFullScreen}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-col w-full gap-3">
                  <div className="flex-1 w-full">
                    <Effectiveness
                      isFullScreen={isFullScreen}
                      setIsFullScreen={setIsFullScreen}
                    />
                  </div>
                  <div className="flex-1 w-full">
                    <Influence
                      isFullScreen={isFullScreen}
                      setIsFullScreen={setIsFullScreen}
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}
      {sourceReport.platform === "news" && (
        <div className="flex gap-3">
          <div className="w-[60%] flex flex-col gap-3">
            <div className="flex-1 flex flex-col">
              <SimilarSources />
            </div>
          </div>

          <div className="w-[40%] flex flex-col gap-3">
            <div className="flex-1 flex flex-col">
              <ThoughtLine />
            </div>
            <div className="flex-1 flex flex-col">
              <PoliticalSpectrum />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(AnalysisTab);
