import PropTypes from "prop-types";
import { useEffect, useRef, useState, useMemo } from "react";
import ForceGraph2D from "react-force-graph-2d";
import { forceCollide } from "d3-force-3d";
import use360requestStore from "store/360requestStore.js";
import { toPersianNumber } from "utils/helper";

const GraphChart = ({
  data = [],
  onLinkSelect,
  onCenterNodeSelect,
  onNodeSelect,
  user,
  isReverse,
  selectedListNode,
}) => {
  const fgRef = useRef();
  const containerRef = useRef();
  const imageCache = useRef({});
  const [hoveredLink, setHoveredLink] = useState(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  const sourceReport = use360requestStore((state) => ({
    profile: state.report?.content?.source_info,
  }));

  const profile = sourceReport.profile;

  // const report = useReport360Store.getState().report || {};
  // const profile = report.profile || {};
  const avatar = profile.avatar || user;
  const name = profile.user_title || "Unknown User";

  // Effect to handle container resizing
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setDimensions({ width: offsetWidth, height: offsetHeight });
      }
    };

    // Initial measurement
    updateDimensions();

    // Set up ResizeObserver for responsive behavior
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  const { nodes, links } = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0)
      return { nodes: [], links: [] };

    const nodesMap = new Map();
    const linksArray = [];

    data.forEach(
      ({
        from,
        to,
        count,
        avatar: nodeAvatar,
        content_query,
        profile_info,
      }) => {
        const fromNode = from || name;
        const toNode = to || "Unknown";

        if (!nodesMap.has(fromNode)) {
          nodesMap.set(fromNode, {
            id: fromNode,
            avatar: fromNode === name ? avatar : nodeAvatar || user,
            level: fromNode === name ? 0 : 1,
            count: count || 1,
          });
        }
        if (!nodesMap.has(toNode)) {
          nodesMap.set(toNode, {
            id: toNode,
            avatar: nodeAvatar || user,
            level: 1,
            count: count || 1,
            content_query: content_query,
            profile_info: profile_info,
          });
        }

        linksArray.push({
          source: fromNode,
          target: toNode,
          value: count || 1,
          count: count || 1,
          content_query: content_query,
          profile_info: profile_info,
        });
      }
    );

    return { nodes: Array.from(nodesMap.values()), links: linksArray };
  }, [data, name, avatar, user]);

  useEffect(() => {
    if (fgRef.current && nodes.length > 0) {
      // Add a small delay to ensure the ForceGraph2D is fully initialized
      const timer = setTimeout(() => {
        if (fgRef.current) {
          fgRef.current.d3Force("collision", forceCollide(8));
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [nodes]);

  useEffect(() => {
    if (fgRef.current) {
      if (hoveredLink) {
        fgRef.current?.d3Force("link")?.strength(0);
        fgRef.current?.d3Force("charge")?.strength(0);
        fgRef.current?.d3Force("collision")?.strength(0);
      } else {
        fgRef.current?.d3Force("link")?.strength(1);
        fgRef.current?.d3Force("charge")?.strength(-30);
        fgRef.current?.d3Force("collision")?.strength(1);
      }
    }
  }, [hoveredLink]);

  const isLinkSelected = (link) => {
    if (!selectedListNode) return false;
    return (
      link.content_query === selectedListNode.content_query &&
      link.profile_info?.user_name ===
        selectedListNode.profile_info?.user_name &&
      link.count === selectedListNode.count
    );
  };

  const isNodeSelected = (node) => {
    if (!selectedListNode || node.id === name) return false;
    return (
      node.content_query === selectedListNode.content_query &&
      node.profile_info?.user_name === selectedListNode.profile_info?.user_name
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        width: "100%",
        height: "100%",
        position: "relative",
        borderRadius: "12px",
        overflow: "hidden",
        transition: "all 0.3s ease-in-out",
      }}
    >
      {dimensions.width > 0 && dimensions.height > 0 && (
        <ForceGraph2D
          ref={fgRef}
          graphData={{ nodes, links }}
          width={dimensions.width}
          height={dimensions.height}
          backgroundColor="transparent"
          linkColor={(link) =>
            isLinkSelected(link) || hoveredLink === link
              ? "rgba(102, 153, 255, 1)"
              : "rgba(170, 170, 170, 0.7)"
          }
          linkWidth={1}
          nodeRelSize={3}
          nodeId="id"
          nodeLabel={(node) => node.id}
          nodeCanvasObject={(node, ctx) => {
            const defaultSize = Math.max(8, 3 + Math.sqrt(node.count) * 3);
            const maxNodeSize = 13;

            const size =
              node.id === name ? 15 : Math.min(defaultSize, maxNodeSize);

            // Draw rounded background circle
            ctx.save();
            ctx.beginPath();
            ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI, false);

            // Set background color (fallback)
            ctx.fillStyle = "#f0f0f0";
            ctx.fill();

            // Load default logo if not cached
            const defaultLogo = "/logo_small.png";
            if (!imageCache.current[defaultLogo]) {
              const defaultImg = new Image();
              defaultImg.crossOrigin = "Anonymous";
              defaultImg.src = defaultLogo;
              defaultImg.onload = () => {
                imageCache.current[defaultLogo] = defaultImg;
              };
              defaultImg.onerror = () => {
                console.warn(`Failed to load default logo: ${defaultLogo}`);
                imageCache.current[defaultLogo] = null;
              };
              imageCache.current[defaultLogo] = defaultImg;
            }

            // Create a pattern from the avatar image if available
            if (!imageCache.current[node.avatar]) {
              const img = new Image();
              img.crossOrigin = "Anonymous";
              img.src = node.avatar || user;
              img.onload = () => {
                imageCache.current[node.avatar] = img;
              };
              img.onerror = () => {
                console.warn(`Failed to load image: ${node.avatar}`);
                // Use default logo as fallback
                imageCache.current[node.avatar] =
                  imageCache.current[defaultLogo];
              };
              imageCache.current[node.avatar] = img;
            }

            const img = imageCache.current[node.avatar];
            const fallbackImg = imageCache.current[defaultLogo];
            const imageToUse =
              img && img.complete && img.naturalWidth > 0 ? img : fallbackImg;

            if (
              imageToUse &&
              imageToUse.complete &&
              imageToUse.naturalWidth > 0
            ) {
              // Clip to circle shape
              ctx.clip();

              // Draw the background image to fill the circle
              try {
                ctx.drawImage(
                  imageToUse,
                  node.x - size / 2,
                  node.y - size / 2,
                  size,
                  size
                );
              } catch (error) {
                console.error(
                  `Error drawing image for node ${node.id}:`,
                  error
                );
              }
            }

            ctx.restore();

            // Draw border/outline
            ctx.beginPath();
            ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI);

            if (node.id === name) {
              ctx.shadowBlur = 10;
              ctx.shadowColor = "rgba(102, 153, 255, 1)";
              ctx.strokeStyle = "rgba(102, 153, 255, 1)";
              ctx.lineWidth = 1;
            } else if (isNodeSelected(node)) {
              ctx.strokeStyle = "rgba(102, 153, 255, 1)";
              ctx.lineWidth = 1;
            } else {
              ctx.strokeStyle = "#ccc";
              ctx.lineWidth = 0.1;
            }

            ctx.stroke();
            ctx.shadowBlur = 0;
          }}
          linkCanvasObjectMode={() => "after"}
          linkCanvasObject={(link, ctx) => {
            const midX = (link.source.x + link.target.x) / 2;
            const midY = (link.source.y + link.target.y) / 2;
            const dx = link.target.x - link.source.x;
            const dy = link.target.y - link.source.y;
            const angle = Math.atan2(dy, dx);
            const offsetY = -2;

            const isHovered = hoveredLink === link;
            const isSelected = isLinkSelected(link);

            ctx.save();
            ctx.translate(midX, midY);
            ctx.rotate(angle);
            ctx.font =
              isHovered || isSelected ? "4px IranYekan" : "3px IranYekan";
            ctx.fillStyle =
              isHovered || isSelected ? "rgba(102, 153, 255, 1)" : "black";
            ctx.shadowBlur = isHovered || isSelected ? 5 : 0;
            ctx.shadowColor = "rgba(255, 111, 97, 0.5)";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(toPersianNumber(link.count), 0, offsetY);
            ctx.shadowBlur = 0;
            ctx.restore();

            const arrowSize = 2;
            const arrowAngle = Math.PI / 4;

            ctx.save();
            ctx.strokeStyle =
              isHovered || isSelected
                ? "rgba(61, 126, 255, 0.8)"
                : "rgba(170, 170, 170, 0.7)";
            ctx.lineWidth = 0.7;
            ctx.fillStyle =
              isHovered || isSelected
                ? "rgba(61, 126, 255, 0.8)"
                : "rgba(170, 170, 170, 0.7)";

            const offset = 0.25;
            const arrowPositionX = midX + offset * dx;
            const arrowPositionY = midY + offset * dy;

            ctx.beginPath();
            ctx.moveTo(arrowPositionX, arrowPositionY);
            if (isReverse) {
              ctx.lineTo(
                arrowPositionX + arrowSize * Math.cos(angle - arrowAngle),
                arrowPositionY + arrowSize * Math.sin(angle - arrowAngle)
              );
              ctx.moveTo(arrowPositionX, arrowPositionY);
              ctx.lineTo(
                arrowPositionX + arrowSize * Math.cos(angle + arrowAngle),
                arrowPositionY + arrowSize * Math.sin(angle + arrowAngle)
              );
            } else {
              ctx.lineTo(
                arrowPositionX - arrowSize * Math.cos(angle - arrowAngle),
                arrowPositionY - arrowSize * Math.sin(angle - arrowAngle)
              );
              ctx.moveTo(arrowPositionX, arrowPositionY);
              ctx.lineTo(
                arrowPositionX - arrowSize * Math.cos(angle + arrowAngle),
                arrowPositionY - arrowSize * Math.sin(angle + arrowAngle)
              );
            }

            ctx.stroke();
            ctx.restore();
          }}
          d3VelocityDecay={0.3}
          zoomToFit
          maxZoom={4}
          minZoom={4}
          d3Force="link"
          onNodeClick={(node) => {
            if (node.id === name) {
              onCenterNodeSelect();
            } else {
              onNodeSelect(node?.content_query, node?.profile_info, node);
            }
          }}
          onLinkHover={(link) => {
            setHoveredLink(link);
          }}
          onLinkClick={(link) => {
            const targetNode = link.target;
            if (targetNode.id === name) return;
            if (onLinkSelect) {
              onLinkSelect(
                targetNode.content_query,
                targetNode.profile_info,
                targetNode.count
              );
            }
          }}
          d3AlphaDecay={0.02}
          cooldownTicks={100}
          onEngineStop={() => {
            if (fgRef.current) {
              fgRef.current.zoomToFit(10);
              // Ensure collision force is applied after engine stops
              fgRef.current.d3Force("collision", forceCollide(8));
            }
          }}
          enableZoomInteraction={false}
          enablePanInteraction={false}
          enableNodeDrag={false}
        />
      )}
    </div>
  );
};

GraphChart.propTypes = {
  ref: PropTypes.object,
  data: PropTypes.array,
  onLinkSelect: PropTypes.func,
  onCenterNodeSelect: PropTypes.func,
  onNodeSelect: PropTypes.func,
  user: PropTypes.string,
  isReverse: PropTypes.bool,
  selectedListNode: PropTypes.object,
};

export default GraphChart;
