import PropTypes from "prop-types";
import PersonBestSource from "../../components/BestSource";
import PersonContentNumber from "../../components/ContentNumber";
import LatestContent from "../../components/LatestContent";
// import OwnedResources from "../../components/OwnedResources";
import PersonReleaseProcess from "../../components/ReleaseProcess";
import PersonRepeatHashtags from "../../components/RepeatHashtags";
import PersonRepeatWords from "../../components/RepeatWords";
import ResourcesInfo from "../../components/ResourcesInfo";
import PersonThematicCategory from "../../components/ThematicCategory";
import PersonSentiment from "../../components/Sentiment";
// import PersonAge from "../../components/Age";
// import PersonGender from "../../components/gender";

const InstagramBased = ({ activeSpan, activePlatform }) => {
  return (
    // <div className="flex flex-col gap-3 px-4">
    //   {/* <OwnedResources activeSpan={activeSpan} /> */}
    //   <ResourcesInfo activeSpan={activeSpan} activePlatform={activePlatform} />
    //   <LatestContent activeSpan={activeSpan} />
    //   <div className="flex gap-3 items-stretch">
    //     <div className="w-3/5">
    //       <PersonReleaseProcess activeSpan={activeSpan} />
    //     </div>
    //     <div className="w-2/5">
    //       <PersonContentNumber />
    //     </div>
    //   </div>
    //   <div className="flex gap-3 items-stretch">
    //     <div className="w-3/5">
    //       <PersonThematicCategory activeSpan={activeSpan} />
    //     </div>
    //     <div className="w-2/5">
    //       <PersonSentiment />
    //     </div>
    //     {/* <div className="flex-1 min-w-0">
    //       <PersonAge />
    //     </div>
    //     <div className="flex-1 min-w-0">
    //       <PersonGender />
    //     </div> */}
    //   </div>
    //   <PersonBestSource />
    //   <div className="flex gap-3 items-end">
    //     <div className="w-[50%]">
    //       <PersonRepeatHashtags />
    //     </div>
    //     <div className="w-[50%]">
    //       <PersonRepeatWords />
    //     </div>
    //   </div>
    // </div>
    <div className="h-[30rem] w-[90%] flex items-center justify-center font-subtitle-medium">
      داده ای برای نمایش وجود ندارد
    </div>
  );
};

InstagramBased.propTypes = {
  activeSpan: PropTypes.oneOf([
    "telegram",
    "twitter",
    "instagram",
    "news",
    "overview",
  ]).isRequired,
  activePlatform: PropTypes.string.isRequired,
};

export default InstagramBased;
