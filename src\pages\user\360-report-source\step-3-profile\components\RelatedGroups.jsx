import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import user from "../../../../../assets/images/360/user.png";
import { useReport360Store } from "store/report360Store";

const RelatedGroups = () => {
  const { profile } = useReport360Store((state) => state.report);
  const platform = profile.platform;

  const items = new Array(5).fill(null);

  let platformName;
  switch (platform) {
    case "twitter":
      platformName = "اکانت توییتر";
      break;
    case "instagram":
      platformName = "اکانت اینستاگرام";
      break;
    case "telegram":
      platformName = "اکانت تلگرام";
      break;
    default:
      platformName = "خبرگزاری";
  }

  return (
    <Card className="flex flex-col gap-2 w-[52rem]">
      <Title title="سازمان‌ها و گروه‌های مرتبط" />
      <div className="flex gap-8 px-5 justify-between">
        {items.map((_, i) => (
          <div key={i} className="flex gap-8">
            <div className="flex flex-col items-center text-center py-3 pt-6">
              <img src={user} alt="user" className="rounded-[50px]" />
              <h3 className="font-subtitle-large">نام {platformName}</h3>
              <span className="font-overline-medium">@twitter-ID</span>
            </div>
            {i < items.length - 1 && <div className="w-px  bg-gray-200"></div>}
          </div>
        ))}
      </div>
    </Card>
  );
};

export default RelatedGroups;
