import { useState, useEffect } from "react";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import "../../style.css";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import Mention<PERSON><PERSON> from "./MentionChart";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";

const RepeatMentions = () => {
  const { profile, date } = useReport360Store((state) => state.report);
  const [loading, setLoading] = useState(false);
  const [info, setInfo] = useState([]);
  const platform = profile.platform;

  const fetchData = async (abortController) => {
    setLoading(true);

    const infoQuery = `search/api/v1/search?api_path=/${platform}/users/by_subject&count=10&from=${parseInt(
      new Date(date.from).getTime() / 1000
    )}&to=${parseInt(
      new Date(date.to).getTime() / 1000
    )}&sort=mentions_count&source=original`;

    try {
      const response = await advanceSearch.search(
        { q: infoQuery },
        abortController
      );

      const data = JSON.parse(response.data.data).result.data;

      const mappedData = data?.slice(0, 8).map((item) => ({
        word: item.username,
        count: item.mentions_count,
        username: item.username,
        avatar: `https://f002.backblazeb2.com/file/all-gather-media/${item.avatar}`,
      }));

      setInfo(mappedData);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    fetchData(abortController);
    return () => abortController.abort();
  }, [date, platform]);

  const getBlueColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));
    const intensity = Math.floor(
      50 + ((count - minCount) / (maxCount - minCount)) * 205
    );
    return `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
  };

  if (loading) {
    return (
      <div className="flex">
        <Card className={`flex flex-col gap-4 w-full`}>
          <Title title="منشن های پرتکرار"></Title>
          <div className="flex w-full h-80 justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex">
      <Card className="px-0 flex flex-col gap-2 card-animation card-delay">
        <Title title="منشن‌های پرتکرار"></Title>
        <p className="px-2 font-body-small text-[#8f8f8f]">
          اکانت‌هایی که این اکانت آن‌ها منشن کرده است
        </p>
        <div className="w-full">
          <div className="grid grid-cols-1">
            <MentionChart
              colors={info?.map((item) => getBlueColor(item.count))}
              info={info}
              categories={info?.map((item) => item.word)}
              loading={loading}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default RepeatMentions;
