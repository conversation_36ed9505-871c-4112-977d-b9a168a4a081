import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import Step3Profile from "./step-3-profile";

const Report360Source = () => {
  let breadcrumbConditional = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "گزارش جدید" },
  ];
  const breadcrumbList = breadcrumbConditional;
  useBreadcrumb(breadcrumbList);

  return (
    <div className="flex-1">
      <Step3Profile />
    </div>
  );
};

export default Report360Source;
