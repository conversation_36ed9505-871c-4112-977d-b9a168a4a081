import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import "../../style.css";
import { useReport360Store } from "store/report360Store";
import MediaBadge from "components/ui/MediaBadge";
import DateFilter from "./DateFilter";
import { CButton } from "components/ui/CButton";
import use360requestStore from "store/360requestStore";
import report360 from "service/api/report360";
import { CInput } from "components/ui/CInput";
import Popup from "components/ui/PopUp";
import { memo, useState, useRef } from "react";
import { notification } from "utils/helper";
import {
  CheckCircle,
  User,
  WarningDiamond,
  Image,
  Link,
} from "@phosphor-icons/react";
import FetchImage from "pages/user/show-profile/components/FetchImage";
import Loading from "components/ui/Loading";
import fallbackImg from "assets/images/default.png";
import { useNavigate } from "react-router-dom";

const UserInfo = ({ isFixed, isEdit }) => {
  const { date, id } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));

  const platform = sourceReport.platform;
  const profile = sourceReport.profile;
  const sourceTitle =
    profile.user_title || profile.channel_title || profile.full_name || "";

  const [isOpen, setIsOpen] = useState(false);

  const getButtonText = () => {
    return isEdit ? "بروزرسانی استعلام" : "ذخیره استعلام";
  };

  const [title, setTitle] = useState(sourceTitle);
  const setReport = useReport360Store((state) => state.setReport);
  const updateReport = use360requestStore((state) => state.updateReportField);

  const [loadingAvatar, setLoadingAvatar] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [backgroundImage, setBackgroundImage] = useState(null);
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setReport({ date: { from, to } });
  };

  const handleTitle = (e) => {
    setTitle(e.target.value);
    updateReport("title", e.target.value);
  };

  const handleAvatarClick = () => fileInputRef.current.click();

  const handleFileChange = async (event) => {
    setLoadingAvatar(true);
    const file = event.target.files[0];

    if (!file) {
      setLoadingAvatar(false);
      return;
    }

    const maxSizeInBytes = 1 * 1024 * 1024; // 1 MB

    if (file.size > maxSizeInBytes) {
      notification.error(
        "حجم فایل انتخاب شده نباید بیشتر از 1 مگابایت باشد.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
      setLoadingAvatar(false);
      return;
    }

    if (file.type === "image/png" || file.type === "image/jpeg") {
      try {
        const formData = new FormData();
        formData.append("image", file);

        const uploadedImageRes = await report360.image(formData);
        const avatarUrl = uploadedImageRes?.data?.data?.image;

        const imageUrl = URL.createObjectURL(file);
        setBackgroundImage(imageUrl);

        setAvatarUrl(avatarUrl);
      } catch (error) {
        notification.error(
          "آپلود تصویر با خطا مواجه شد.",
          <WarningDiamond size={32} className="text-light-error-text-rest" />
        );
      }
    } else {
      notification.error(
        "فرمت فایل انتخاب شده صحیح نیست.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }

    setLoadingAvatar(false);
  };

  // useEffect(() => {
  //   if (title === "") {
  //     updateReport(
  //       "title",
  //       currentProfile.user_title ||
  //         currentProfile?.channel_title ||
  //         currentProfile.full_name
  //     );
  //   } else {
  //     updateReport("title", title);
  //   }
  // }, [
  //   currentProfile.user_title,
  //   currentProfile.channel_title,
  //   currentProfile.full_name,
  //   title,
  //   updateReport,
  // ]);

  const handleReportSave = async () => {
    try {
      let response;
      updateReport("description", profile?.bio || "");
      updateReport("image", avatarUrl ?? profile?.avatar ?? profile.logo_image);
      updateReport("q", sourceTitle);
      updateReport("title", title || sourceTitle);

      const reportState = use360requestStore.getState().report;

      if (isEdit) {
        if (!id) {
          notification.error("شناسه گزارش معتبر نیست.");
          return;
        }
        response = await report360.update(id, reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ۳۶۰ با موفقیت بروزرسانی شد`,
            <CheckCircle className="text-light-success-text-rest" />
          );
        }
      } else {
        response = await report360.create(reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ۳۶۰ با موفقیت ذخیره شد. در حال انتقال به لیست گزارش‌ها...`,
            <CheckCircle className="text-light-success-text-rest" />
          );
          setTimeout(() => navigate(`/app/report-360/list`), 3000);
        }
      }
      setIsOpen(false);
    } catch (error) {
      console.error("Error saving report:", error);
      notification.error(
        `با عرض پوزش، امکان ذخیره 'گزارش' وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`
      );
    }
  };

  const handleKeyDown = async (e) => {
    if (e.key === "Enter") {
      await handleReportSave();
    }
  };

  const getPlatformUrl = (platform, username) => {
    if (!username) return "#";
    const baseUrls = {
      telegram: `https://t.me/${username}`,
      instagram: `https://www.instagram.com/${username}`,
      twitter: `https://twitter.com/${username}`,
      news: username,
    };
    return baseUrls[platform] || "#";
  };

  return (
    <>
      <Card
        className={`flex justify-between items-center !py-2 !px-6 gap-5 border-r-8 border-[#4D36BF] shadow-xl ${
          isFixed && "mt-11 shadow-xl info-box"
        } card-animation card-delay mb-4`}
      >
        <div className="flex flex-col gap-5">
          <div className="flex items-center gap-3 ">
            <div
              className={`relative w-[5rem] h-[5rem] rounded-full z-[10] mask-image-avatar ${
                isFixed ? "!w-[4rem] !h-[4rem]" : ""
              }`}
              style={{
                backgroundImage: `url(${
                  profile.avatar || profile.logo_image
                }), url(/logo_small.png)`,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
                backgroundPosition: "center center",
              }}
            >
              <span
                className={`absolute ${
                  !isFixed ? "bottom-0" : "bottom-0"
                } right-0 rounded-full !w-[30px] !h-[30px] z-[10]`}
              >
                <MediaBadge
                  media={platform}
                  className="!h-[30px] !w-[30px] !rounded-[50px]"
                  size="normal"
                />
              </span>
            </div>

            <div className="flex flex-col gap-1">
              <div className="flex gap-2 items-center">
                <h3 className="font-headline-small">{sourceTitle}</h3>
                <a
                  className="font-headline-small pb-1 hover:text-blue-500"
                  href={getPlatformUrl(
                    platform,
                    profile?.user_name ||
                      profile?.channel_username ||
                      profile?.base_url
                  )}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Link />
                </a>
              </div>
              {platform === "news" ? (
                <p className="font-body-small text-[#8f8f8f]">
                  {profile.base_url}
                </p>
              ) : (
                <p className="font-body-small text-[#8f8f8f]">
                  {profile.user_name ||
                    profile.source_name ||
                    profile.channel_username}
                  @
                </p>
              )}
            </div>
          </div>
          {/* {!isFixed && (
            <p className="font-body-small w-[80%]">
              {profile.bio || profile.description}
            </p>
          )} */}
        </div>
        <div>
          <div className="flex pt-1 gap-10 z-[10]">
            <DateFilter
              handleDateChange={handleDateChange}
              selectedDateRange={date}
            />
            <CButton
              type={"submit"}
              onClick={() => (!isEdit ? setIsOpen(true) : handleReportSave())}
              size={isFixed ? "md" : "lg"}
              className={"[direction:rtl] [width:170px!important]"}
            >
              {getButtonText()}
            </CButton>
          </div>
        </div>
      </Card>
      <Popup
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={"ذخیره گزارش ۳۶۰"}
        submitHandler={handleReportSave}
      >
        <div className="flex flex-col gap-6">
          <div className={"flex justify-center mb-4"}>
            <FetchImage
              imageUrl={backgroundImage}
              onFetchSuccess={setBackgroundImage}
            />
            <div
              className={
                "flex relative cursor-pointer w-[120px] h-[120px] bg-light-neutral-background-medium" +
                " items-center align-middle text-center group bg-center bg-no-repeat bg-contain"
              }
              style={{
                clipPath: "circle(50%)",
                backgroundImage: backgroundImage
                  ? `url(${backgroundImage})`
                  : "none",
              }}
              onClick={handleAvatarClick}
            >
              {!loadingAvatar ? (
                !backgroundImage && (
                  <User
                    className={"w-full text-light-primary-text-rest"}
                    size={40}
                  />
                )
              ) : (
                <Loading className={"relative w-full h-full"} />
              )}
              <div
                className={
                  "flex w-full absolute bottom-0 bg-light-primary-background-highlight text-light-neutral-text-low h-[22px] text-center opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
                }
              >
                <Image size={20} style={{ margin: "0 auto" }} />
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                max={"1m"}
                onChange={handleFileChange}
              />
            </div>
          </div>
          <div className="font-overline-large">
            این گزارش را با نتایج موجود ذخیره کنید تا در آینده بتوانید به آن
            رجوع کنید.
          </div>
          <div>
            <CInput
              title="عنوان گزارش"
              type="text"
              onChange={handleTitle}
              value={title}
              placeholder={`برای گزارش ۳۶۰ منبع یک عنوان بنویسید`}
              inputProps={{ onKeyDown: handleKeyDown }}
            />
          </div>
        </div>
      </Popup>
    </>
  );
};

UserInfo.propTypes = {
  isFixed: PropTypes.bool.isRequired,
  isEdit: PropTypes.bool,
};

export default memo(UserInfo);
