import React, { useState } from "react";
import SummaryCard from "components/SummaryCard";
import PropTypes from "prop-types";
import TextSlicer from "components/TextSlicer";
import { findPlatform } from "pages/user/hot-topic/utils";
import clsx from "clsx";
import { useReport360Store } from "store/report360Store";

const truncateText = (text, maxLength = 220) => {
  if (!text) return "";
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};

const SummaryCard360 = ({
  data,
  media,
  selected = false,
  platform,
  isDropdownOpen,
}) => {
  const [select, setSelect] = useState(selected);
    const { profile } = useReport360Store((state) => state.report);
  

  const description =
    media === "news" && data.content
      ? data.content
      : data.description || data?.text;
  const truncatedDescription = truncateText(description);

  return (
    <div
      className={clsx(
        "bg-light-neutral-surface-card rounded-lg shadow-[0px_2px_20px_0px_#0000000D] !w-[96%] h-[90%] mb-5",
        select && "outline outline-1 outline-light-primary-border-rest"
      )}
    >
      <SummaryCard
        media={findPlatform(data)}
        data={data}
        platform={platform}
        showMediaName
        is360={true}
        profile360={profile}
        handleSelect={setSelect}
        isDropdownOpen={isDropdownOpen}
      >
        <TextSlicer media={media}>
          <p>{truncatedDescription}</p>
        </TextSlicer>
      </SummaryCard>
    </div>
  );
};

export default React.memo(SummaryCard360);

SummaryCard360.propTypes = {
  data: PropTypes.object.isRequired,
  media: PropTypes.string.isRequired,
  platform: PropTypes.string.isRequired,
  selected: PropTypes.bool,
};
