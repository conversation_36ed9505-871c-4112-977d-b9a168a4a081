import { useState } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { fixPercentToShow } from "utils/helper";
import { gender } from "utils/selectIcon";
import { SpinnerGap } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import "../../style.css";
import { Card } from "components/ui/Card";

const PersonGender = ({ showDataLabels }) => {
  const [loading] = useState(false);

  const data = {
    female: 30,
    male: 50,
    org: 20,
  };

  const percent = (value, all) => {
    const sum = Object.values(all).reduce((prev, cur) => prev + cur, 0);
    return (value / sum) * 100;
  };

  if (loading) {
    return (
      <div className="w-full h-[365px] flex justify-center items-center">
        <SpinnerGap size={40} className="animate-spin" />
      </div>
    );
  }

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay w-full">
      <p className="font-subtitle-large text-right">دسته‌بندی جنسیت</p>
      <Divider />
      {data && Object.values(data).length === 0 ? (
        <div className="h-full flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <Doughnut
          showDataLabels={showDataLabels}
          colLayout
          name="gender"
          height={365}
          data={[
            {
              name: "زن",
              y: +percent(data.female, data).toFixed(2),
            },
            {
              name: "مرد",
              y: +percent(data.male, data).toFixed(2),
            },
            {
              name: "نامشخص",
              y: +percent(data.org, data).toFixed(2),
            },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="display:grid;grid-template-columns:1fr 1fr; gap: 20px; font-family:iranyekan;width:260px;padding:2px; margin-right: 1rem;">
              <span style="font-size:16px">
                ${fixPercentToShow(this.y / 100)}
              </span>
              <div style="display: flex; gap: 4px; align-items: center; justify-content: flex-end;">
                <div style="display:flex;gap:4px">
                  <span style="color:${this.color};font-size:14px">${
              this.name
            }</span>
                  <img src="${gender[this.name]}" alt="icon" />
                </div>
              </div>
            </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${
              this.key
            }</div><div>${fixPercentToShow(this.y / 100)}</div></div>`;
          }}
          colors={["#E052B8", "#1C60B0", "#00000080"]}
        />
      )}
    </Card>
  );
};

PersonGender.propTypes = {
  showDataLabels: PropTypes.bool,
};

export default PersonGender;
