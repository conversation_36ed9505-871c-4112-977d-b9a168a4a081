import { memo } from "react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import twitter from "../../../../../assets/images/360/twitter.png";
import telegram from "../../../../../assets/images/360/telegram.png";
import news from "../../../../../assets/images/360/news.png";
import {
  formatShortNumber,
  parseNumber,
  parseTimeToPersianSummary,
  toPersianNumber,
} from "utils/helper";
import "../../style.css";
import use360requestStore from "store/360requestStore";
import fallbackImg from "assets/images/default.png";
import ExportMenu from "components/ExportMenu/index.jsx";
import Divider from "../../../../../components/ui/Divider";

const Info = () => {
  const sourceReport = use360requestStore((state) => ({
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));
  const platform = sourceReport.platform;
  const profile = sourceReport.profile;

  const series = [
    {
      name: "Profile Metrics",
      data: [
        platform === "telegram"
          ? profile.member_count || 0
          : platform === "news"
          ? profile.post_count || 0
          : profile.follower_count || 0,
        platform === "telegram"
          ? (profile.photo_count || 0) +
            (profile.link_count || 0) +
            (profile.file_count || 0) +
            (profile.video_count || 0)
          : platform === "news"
          ? profile.post_count || 0
          : profile.tweet_count || 0,
        platform === "telegram"
          ? (profile.video_count || 0) + (profile.photo_count || 0)
          : 0,
      ].filter((val) => val !== null),
      time: [
        platform === "telegram"
          ? "تعداد اعضاء"
          : platform === "news"
          ? "تعداد محتوا"
          : "دنبال کننده",
        platform === "telegram"
          ? "پست"
          : platform === "news"
          ? "تعداد محتوا"
          : "توئیت",
        platform === "telegram" ? "محتوای تصویری" : null,
      ].filter((val) => val !== null),
    },
  ];

  const time = series[0].time;

  const getPlatformName = () => {
    switch (platform) {
      case "instagram":
        return "اینستاگرام";
      case "twitter":
        return "توییتر";
      case "telegram":
        return "تلگرام";
      case "news":
        return "خبر";
      default:
        return "بستر";
    }
  };

  return (
    <Card className="flex flex-col gap-2 card-animation card-delay h-full">
      <div className="flex items-center justify-between">
        <Title title="شناسنامه" pos="left">
          {`این گزارش مشخصات کلی و آماری پروفایل مورد نظر را از ابتدای ایجاد آن در بستر (( ${getPlatformName()} )) نشان می‌دهد`}
        </Title>
        <ExportMenu
          chartSelector=".info-container"
          fileName="profile-info"
          series={series}
          time={time}
          excelHeaders={["Metric", "Value"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="شناسنامه منبع"
        />
      </div>
      <div className="relative info-container">
        <div>
          <div
            className={`absolute top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full z-[10] mask-image-avatar`}
            style={{
              backgroundImage: `url(${
                profile.avatar || profile.logo_image
              }), url(/logo_small.png)`,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPosition: "center center",
            }}
          ></div>
        </div>
        <h3 className="absolute top-[19%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `صاحب امتیاز`
            : platform === "telegram"
            ? `نوع منبع`
            : "دنبال شونده"}
        </h3>
        <span className="absolute top-[28%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 w-28 text-center text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "news" && profile?.founder != null
            ? profile.founder
            : platform === "telegram"
            ? profile.type
            : profile.following_count
            ? toPersianNumber(parseNumber(profile.following_count))
            : "---"}
        </span>
        {platform !== "telegram" && platform !== "news" && (
          <>
            <h3 className="absolute top-[45%] left-[14%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
              دنبال کننده
            </h3>
            <span className="absolute top-[54%] left-[14%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
              {profile.follower_count
                ? toPersianNumber(parseNumber(profile.follower_count))
                : "---"}
            </span>
          </>
        )}
        <h3 className="absolute top-[71%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `تعداد محتوا`
            : platform === "telegram"
            ? "تعداد اعضاء"
            : "توئیت"}
        </h3>
        <span className="absolute top-[80%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "news" && profile.post_count !== null
            ? toPersianNumber(parseNumber(profile?.post_count))
            : platform === "telegram"
            ? toPersianNumber(formatShortNumber(profile.member_count))
            : profile?.tweet_count
            ? toPersianNumber(parseNumber(profile?.tweet_count))
            : "---"}
        </span>
        <h3 className="absolute top-[19%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `سال تاسیس`
            : platform === "telegram"
            ? "پست"
            : "تاریخ عضویت"}
        </h3>
        <span className="absolute top-[28%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {profile.founded_year != null
            ? toPersianNumber(profile.founded_year)
            : platform === "telegram"
            ? toPersianNumber(
                formatShortNumber(
                  profile?.photo_count +
                    profile?.link_count +
                    profile?.file_count +
                    profile?.video_count
                )
              )
            : profile.join_date != null
            ? parseTimeToPersianSummary(profile.join_date)
            : "---"}
        </span>
        {platform !== "telegram" && platform !== "news" && (
          <>
            <h3 className="absolute top-[45%] left-[86%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
              موقعیت
            </h3>
            <span className="absolute top-[54%] left-[86%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
              {profile.location || "---"}
            </span>
          </>
        )}
        <h3 className="absolute top-[71%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `محل ستاد`
            : platform === "telegram"
            ? "محتوای تصویری"
            : "جنسیت"}
        </h3>
        <span className="absolute top-[80%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "telegram"
            ? toPersianNumber(
                formatShortNumber(profile?.video_count + profile?.photo_count)
              )
            : platform === "news"
            ? profile?.office_location || "---"
            : profile?.gender?.label === "female"
            ? "زن"
            : profile?.gender?.label === "male"
            ? "مرد"
            : "---"}
        </span>
        <img
          src={
            platform === "telegram"
              ? telegram
              : platform === "news"
              ? news
              : twitter
          }
          className="w-full px-2"
          draggable="false"
          alt="diagram"
        />
      </div>
      {(profile.bio || profile.description) && (
        <>
          <Divider />
          <div>
            <h5 className="font-subtitle-medium mb-1">بیوگرافی صفحه کاربر</h5>
            <p className="font-body-small w-[80%]">
              {profile.bio || profile.description}
            </p>
          </div>
        </>
      )}
    </Card>
  );
};

export default memo(Info);
