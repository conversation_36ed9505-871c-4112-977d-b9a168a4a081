import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import { buildRequestData } from "utils/requestData";
import Pie<PERSON>hart from "./PieChart";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import { Card } from "components/ui/Card";
import usePlatformDataStore from "store/person360platform";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const getPersianLabel = (key) => {
  const labels = {
    "non-adv": "غیر تبلیغاتی",
    adv: "تبلیغاتی",
  };
  return labels[key] || key;
};

const getColorByKey = (key) => {
  const colors = {
    "non-adv": "#2EB6AC",
    adv: "#F96B83",
  };
  return colors[key] || "#cccccc";
};

const PersonAdvertise = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const { date, entity, type } = useReport360Store((state) => state.report);
  const report = use360requestStore((state) => state.report);
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const [isDateChange, setIsDateChange] = useState(false); // Track date changes
  const { platformData, setPlatformData } = usePlatformDataStore();
  const platform = "telegram";
  const chartData = report[reportType]?.telegram?.advertise;

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Content Type",
      data: data.map((item) => item.count),
      time: data.map((item) => getPersianLabel(item.key)),
    },
  ];

  const time = data.map((item) => getPersianLabel(item.key));

  const getData = async (abortController) => {
    // Check cache first, but ignore cache if date has changed
    const cachedData = platformData[platform]?.advertise;
    if (cachedData && !isDateChange) {
      setData(cachedData);
      setLoading(false);
      return;
    }

    // Use chartData if data exists and date hasn't changed
    if (chartData?.length > 0 && !isDateChange) {
      setData(chartData);
      setPlatformData(platform, "advertise", chartData);
      setLoading(false);
      return;
    }

    // Fetch new data if no cache or date has changed
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          date,
          platform,
          q: entity,
        },
        "advertise"
      );

      const res = await advanceSearch.search(requestData, abortController);

      const stinasData = res?.data?.data?.telegram || [];

      const transformedData = stinasData.map((item) => ({
        key: item.key,
        count: item.count,
      }));

      setData(transformedData);
      setPlatformData(platform, "advertise", transformedData);
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
      }
      setData([]);
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
        setIsDateChange(false); // Reset date change flag after fetching
      }
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    getData(abortController);
    return () => abortController.abort();
  }, [date, entity]);

  // Detect date changes and set isDateChange to true
  useEffect(() => {
    setIsDateChange(true);
  }, [date]);

  const pieChartData = data.map((item) => ({
    name: getPersianLabel(item.key),
    y: Number(item.count) || 0,
    color: getColorByKey(item.key),
    sliced: item.key === "adv",
    selected: item.key === "adv",
  }));

  return (
    <Card className="card-animation flex-1 card-delay !h-full">
      <div className="flex flex-col gap-2 w-full">
        <div className="flex items-center justify-between">
          <Title title="تحلیل محتوای تبلیغاتی" />
          <ExportMenu
            chartSelector=".person-advertise-container"
            fileName="person-advertise"
            series={series}
            time={time}
            excelHeaders={["Content Type", "Count"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            chartTitle="تحلیل محتوای تبلیغاتی"
          />
        </div>
        <div className="w-full person-advertise-container flex flex-col px-3 h-full items-center justify-center">
          {loading ? (
            <div className="h-full flex items-center justify-center font-subtitle-medium">
              در حال بارگذاری...
            </div>
          ) : data.length > 0 && pieChartData.some((item) => item.y > 0) ? (
            <React.Fragment>
              <div className="w-[70%] flex justify-center">
                <PieChart data={pieChartData} />
              </div>
              <div className="flex gap-7 justify-center">
                {data.map((item, i) => (
                  <div key={i} className="flex gap-2 justify-between">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: getColorByKey(item.key) }}
                      ></div>
                      {getPersianLabel(item.key)}
                    </div>
                    <div>{`(${toPersianNumber(
                      formatShortNumber(item.count)
                    )})`}</div>
                  </div>
                ))}
              </div>
            </React.Fragment>
          ) : (
            <div className="h-full flex items-center justify-center font-subtitle-medium">
              داده ای برای نمایش وجود ندارد
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

PersonAdvertise.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
  showDataLabels: PropTypes.bool,
};

export default PersonAdvertise;
