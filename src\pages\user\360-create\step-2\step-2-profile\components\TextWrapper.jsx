import DOMPurify from "dompurify";

const TextWrapper = ({ children }) => {
  const sanitizedText = DOMPurify.sanitize(children, {
    FORBID_ATTR: ["class"],
  });

  return (
    <div
      className="font-body-medium text-light-neutral-text-high scrollbar-thin px-3"
      style={{
        maxHeight: "110px", 
        overflowY: "scroll",
        paddingRight: "10px", 
      }}
    >
      <div
        className="text-justify"
        style={{ wordBreak: "break-word" }}
        dangerouslySetInnerHTML={{
          __html: sanitizedText,
        }}
      />
    </div>
  );
};

export default TextWrapper;
