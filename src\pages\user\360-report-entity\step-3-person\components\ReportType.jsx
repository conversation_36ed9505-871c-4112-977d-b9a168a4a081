import PropTypes from "prop-types";
import {
  Eyeglasses,
  Instagram<PERSON>ogo,
  Rss,
  Telegram<PERSON>ogo,
  XLogo,
} from "@phosphor-icons/react";

const PersonReportsType = ({ activeTab, setActiveSpan }) => {
  const tabs = [
    { name: "overview", label: "نمای کلی", icon: <Eyeglasses /> },
    {
      name: "twitter",
      label: "توئیتر",
      icon: <XLogo />,
      platform: "twitter",
    },
    {
      name: "instagram",
      label: "اینستاگرام",
      icon: <InstagramLogo />,
      platform: "instagram",
    },
    {
      name: "telegram",
      label: "تلگرام",
      icon: <TelegramLogo />,
      platform: "telegram",
    },
    {
      name: "news",
      label: "سایت‌های خبری",
      icon: <Rss />,
      platform: "news",
    },
  ].filter(Boolean);

  const handleSpanClick = (spanName) => {
    setActiveSpan(spanName);
  };

  const getSpanClass = (spanName) =>
    `flex items-center gap-2 duration-200 p-2 rounded-lg cursor-pointer relative font-body-medium ${
      activeTab === spanName
        ? "text-[#6F5CD1] after:content-[''] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-[2px] after:bg-[#6F5CD1]"
        : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
    }`;

  return (
    <div className="mr-4">
      <div className="font-body-small gap-3 flex pb-1 border-b border-gray-200 relative">
        {tabs.map((tab, index) => (
          <span
            key={index}
            className={getSpanClass(tab.name)}
            onClick={() => handleSpanClick(tab.name)}
          >
            {tab.icon}
            {tab.label}
          </span>
        ))}
      </div>
    </div>
  );
};

export default PersonReportsType;

PersonReportsType.propTypes = {
  activeTab: PropTypes.string.isRequired,
  setActiveSpan: PropTypes.func.isRequired,
};
