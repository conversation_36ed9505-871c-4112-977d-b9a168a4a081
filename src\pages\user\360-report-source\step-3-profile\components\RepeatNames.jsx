import { useState, useEffect, memo } from "react";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import { toPersianNumber } from "utils/helper";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import DropDown from "components/ui/DropDown";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const RepeatNames = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    frequent_entities: state.report?.content?.report_info?.frequent_entities,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const profile = sourceReport.profile;
  const platform = sourceReport.platform;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [activeTab, setActiveTab] = useState("اشخاص");
  const [activeTabName, setActiveTabName] = useState("person");
  const [frequentEntities, setFrequentEntities] = useState({
    person: [],
    location: [],
    event: [],
    organ: [],
  });

  const series = [
    {
      name: activeTab,
      data: wordCloudData.map(({ value }) => value),
      time: wordCloudData.map(({ text }) => text),
    },
  ];

  const time = wordCloudData.map(({ text }) => text);

  const getData = async () => {
    setLoading(true);
    try {
      const filters = {
        date,
        platform: sourceReport.platform,
        sources: [sourceId.toString()],
      };

      const requestData = buildRequestData(filters, "cloud", 25);
      const res = await advanceSearch.search(requestData, null, {
        cloud_type: "entities",
      });

      const responseData = res?.data?.data?.[sourceReport.platform] || {};

      if (!responseData || Object.keys(responseData).length === 0) {
        setFrequentEntities({
          person: [],
          location: [],
          event: [],
          organ: [],
        });
        setWordCloudData([]);
        return;
      }

      const updatedEntities = { ...frequentEntities };

      Object.keys(updatedEntities).forEach((entityType) => {
        const platformData = responseData[entityType] || [];
        updatedEntities[entityType] = platformData.map(({ key, count }) => ({
          key,
          count,
        }));
      });

      setFrequentEntities(updatedEntities);
      updateReportField(
        "content.report_info.frequent_entities",
        updatedEntities
      );

      const newWordCloudData =
        updatedEntities[activeTabName]?.map(({ key, count }) => ({
          text: key,
          value: count,
        })) || [];

      setWordCloudData(newWordCloudData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setFrequentEntities({
        person: [],
        location: [],
        event: [],
        organ: [],
      });
      setWordCloudData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (
      sourceReport.frequent_entities &&
      typeof sourceReport.frequent_entities === "object" &&
      Object.keys(sourceReport.frequent_entities).length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      const newWordCloudData =
        sourceReport.frequent_entities?.[activeTabName]?.map(
          ({ key, count }) => ({
            text: key,
            value: count,
          })
        ) || [];

      setFrequentEntities(sourceReport.frequent_entities);
      setWordCloudData(newWordCloudData);
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [date, activeTabName]);

  useEffect(() => {
    if (frequentEntities && Object.keys(frequentEntities).length > 0) {
      const validTabNames = ["person", "location", "event", "organ"];
      if (
        validTabNames.includes(activeTabName) &&
        frequentEntities[activeTabName]?.length
      ) {
        const newWordCloudData = frequentEntities[activeTabName].map(
          ({ key, count }) => ({
            text: key,
            value: count,
          })
        );
        setWordCloudData(newWordCloudData);
      } else {
        setWordCloudData([]);
      }
    }
  }, [activeTabName, frequentEntities]);

  const tabs = [
    { id: "event", title: "رویداد" },
    { id: "location", title: "مکان ها" },
    { id: "organ", title: "سازمان ها" },
    { id: "person", title: "اشخاص" },
  ];

  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  return (
    <div className="flex h-full">
      <Card className="px-0 card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row items-center gap-10 w-full justify-between">
            <div className="flex items-center">
              <Title title="موجودیت های پرتکرار" />
              <ExportMenu
                chartSelector=".repeat-names-container"
                fileName={`entities-${activeTabName}`}
                series={series}
                time={time}
                excelHeaders={["Entity", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="موجودیت های پرتکرار"
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="tabs-360 gap-3 px-2 font-overline-medium">
                {tabs.map((tab, i) => (
                  <span
                    key={i}
                    onClick={() => {
                      setActiveTab(tab.title);
                      setActiveTabName(tab.id);
                    }}
                    className={`duration-200 cursor-pointer p-2 rounded ${
                      activeTab === tab.title
                        ? "text-[#6F5CD1] bg-[#E9E6F7]"
                        : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
                    }`}
                  >
                    {tab.title}
                  </span>
                ))}
              </div>
            </div>
            <div className="dropdown-360">
              <DropDown
                title="انتخاب موجودیت"
                subsets={tabs.map((tab) => tab.title)}
                selected={activeTab}
                setSelected={(value) => {
                  setActiveTab(value);
                  const selectedTab = tabs.find((tab) => tab.title === value);
                  if (selectedTab) {
                    setActiveTabName(selectedTab.id);
                  }
                }}
              />
            </div>
          </div>
          <div className="repeat-names-container grid grid-cols-1 h-full">
            <div className="flex flex-1 justify-center items-center h-full responsive-svg">
              {loading ? (
                <div className="flex w-full h-80 justify-center items-center">
                  <SpinnerGap size={40} className="animate-spin" />
                </div>
              ) : wordCloudData?.length ? (
                <ReactWordcloud
                  options={{
                    rotations: 1,
                    rotationAngles: [0],
                    enableTooltip: true,
                    deterministic: false,
                    fontFamily: "iranyekan",
                    fontSizes: [14, 54],
                    padding: 10,
                    colors: CLUSTER_COLORS,
                    tooltipOptions: { theme: "light", arrow: true },
                  }}
                  words={wordCloudData}
                  callbacks={{
                    getWordTooltip: (word) => {
                      const countInPersian = toPersianNumber(word?.value);
                      return `${word.text} (${countInPersian})`;
                    },
                    onWordClick: handleWordClick,
                  }}
                />
              ) : (
                <div className="h-72 flex items-center justify-center font-subtitle-medium">
                  داده ای برای نمایش وجود ندارد
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent
            word={word}
            source={sourceId.toString()}
            platform={platform}
            date={date}
          />
        </Drawer>
      )}
    </div>
  );
};

export default memo(RepeatNames);
