import PropTypes from "prop-types";
import Badge from "pages/user/advanced-search/components/stats/Badge";
import {
  Broadcast,
  ChatTeardropDots,
  Eye,
  Heart,
  Repeat,
  ShareFat,
  UserFocus,
} from "@phosphor-icons/react";
import use360requestStore from "store/360requestStore";
import { memo } from "react";

const ResourcesInfo = ({ activePlatform }) => {
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info?.entity,
    statistical: state.report?.content?.report_info?.statistical,
  }));

  const data = sourceReport.statistical?.[activePlatform] || [];

  const staticPlatformData = {
    telegram: [
      {
        title: "تعداد بازنشر",
        icon: <ShareFat color="white" />,
        color: "text-[#C70077]",
        bgColor: "bg-[#C70077]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "repost")?.count || "0"
          : "0",
      },
      {
        title: "تعداد بازدید",
        icon: <Eye color="white" />,
        color: "text-[#0A55E1]",
        bgColor: "bg-[#0A55E1]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "view")?.count || "0"
          : "0",
      },
    ],
    twitter: [
      {
        title: "تعداد ریتوئیت",
        icon: <Repeat color="white" />,
        color: "text-[#3EA9F8]",
        bgColor: "bg-[#3EA9F8]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "retweet")?.count || "0"
          : "0",
      },
      {
        title: "تعداد لایک",
        icon: <Heart color="white" />,
        color: "text-[#E0526A]",
        bgColor: "bg-[#E0526A]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "like")?.count || "0"
          : "0",
      },
    ],
    instagram: [
      {
        title: "تعداد کامنت‌ها",
        icon: <ChatTeardropDots color="white" />,
        color: "text-[#1CB0A5]",
        bgColor: "bg-[#1CB0A5]",
        count: data?.comment_count || "0",
      },
      {
        title: "تعداد لایک",
        icon: <Heart color="white" />,
        color: "text-[#E0526A]",
        bgColor: "bg-[#E0526A]",
        count: data?.like_count || "0",
      },
    ],
    news: [],
  };

  return (
    <div
      className={`${
        activePlatform === "news"
          ? "flex flex-col w-full h-full"
          : "grid grid-cols-12"
      } gap-6 !w-full`}
    >
      <div className="col-span-3 w-full">
        <Badge
          count={
            data.post_count ||
            (Array.isArray(data) &&
              data.find((item) => item.key === "post")?.count) ||
            "0"
          }
          title="تعداد محتوای منتشر شده"
          image="/net2.svg"
          color="text-light-warning-text-rest"
          platform={activePlatform}
          bgColor="bg-light-warning-text-rest"
          icon={<Broadcast color="white" />}
        />
      </div>
      <div className="col-span-3 w-full">
        <Badge
          count={
            data.agency_count ||
            (Array.isArray(data) &&
              data.find((item) => item.key === "source")?.count) ||
            "0"
          }
          title="تعداد منابع انتشار"
          image="/net1.svg"
          color="text-[#AE2EEC]"
          platform={activePlatform}
          bgColor="bg-[#AE2EEC]"
          icon={<UserFocus color="white" />}
        />
      </div>
      {staticPlatformData[activePlatform]?.map((item, index) => (
        <div className="col-span-3" key={item.title}>
          <Badge
            count={item.count}
            title={item.title}
            image={index === 0 ? "/net3.svg" : "/net4.svg"}
            color={item.color}
            bgColor={item.bgColor}
            icon={item.icon}
          />
        </div>
      ))}
    </div>
  );
};

ResourcesInfo.propTypes = {
  activePlatform: PropTypes.oneOf([
    "telegram",
    "twitter",
    "instagram",
    "news",
    "overview",
  ]).isRequired,
};

export default memo(ResourcesInfo);
