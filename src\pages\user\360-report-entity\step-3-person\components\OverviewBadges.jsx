import { formatShortNumber, toPersianNumber } from "utils/helper";

import PropTypes from "prop-types";
import Divider from "components/ui/Divider";

const OverviewBadges = ({
  title,
  count,
  variant,
  color,
  icon: Icon,
  subtitle,
  subCount,
}) => {
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg flex gap-3 !w-full px-2 py-3"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="w-[80%]">
        <div className="flex items-start justify-between">
          <div>
            <p className="font-subtitle-medium text-light-neutral-text-low">
              {title}
            </p>
            <p
              className="font-title-medium font-bold"
              style={{ color: "#525252" }}
            >
              {toPersianNumber(formatShortNumber(count))}
            </p>
          </div>
        </div>
        <Divider />
        <div className="flex items-center justify-start gap-8 px-1 pt-3">
          <p className="font-body-medium text-light-neutral-text-low">
            {subtitle}
          </p>
          <div className="flex items-center">
            <p className={`font-body-medium pl-2`}>
              <span className="font-semi-bold">
                {toPersianNumber(formatShortNumber(subCount))}
              </span>
            </p>
          </div>
        </div>
      </div>
      <div className="w-[20%] flex justify-end">
        <div
          className={`rounded-xl flex items-center justify-center !w-[3.5rem] h-[3.5rem] ml-1 p-2`}
          style={{
            background: variant,
          }}
        >
          <Icon size={40} color={color} />
        </div>
      </div>
    </div>
  );
};

OverviewBadges.propTypes = {
  title: PropTypes.string.isRequired,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  subCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    .isRequired,
  status: PropTypes.oneOf(["asc", "desc"]).isRequired,
  variant: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
  icon: PropTypes.elementType.isRequired,
  subtitle: PropTypes.string,
};

export default OverviewBadges;
