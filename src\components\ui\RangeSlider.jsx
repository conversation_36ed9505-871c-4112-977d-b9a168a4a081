import PropTypes from "prop-types";
import { toP<PERSON>ian<PERSON>umber } from "utils/helper";
import { useRef, useEffect, memo } from "react";

const RangeSlider = ({ value, values, min = 0, onChange = () => {} }) => {
  const sliderRef = useRef(null);

  useEffect(() => {
    if (sliderRef.current) {
      const percentage = ((value - min) / (values.length - 1 - min)) * 100;
      // Reverse the gradient for RTL
      sliderRef.current.style.background = `linear-gradient(to left, #432fa7 ${percentage}%, transparent ${percentage}%)`;
    }
  }, [value, values.length, min]);

  return (
    <div className="w-full mx-auto select-none">
      <div className="relative">
        <div className="relative w-full">
          <input
            ref={sliderRef}
            type="range"
            min={min}
            max={values?.length - 1}
            value={value}
            onChange={onChange}
            step="1"
            className="w-full range-square absolute top-0 left-0 z-10"
            dir="rtl" // Reverse the slider direction
          />
          <div className="w-full flex justify-between absolute top-1/2 left-0 transform -translate-y-1/2 pointer-events-none">
            {Array.from({ length: values?.length }, (_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index <= value ? "bg-[#432FA7]" : "bg-gray-400"
                }`}
              ></div>
            ))}
          </div>
        </div>
        <div className="flex justify-between mt-6">
          {values?.map((item, index) => (
            <div key={index} className="flex flex-col items-center">
              <span className="font-overline-small pt-4">{item.label}</span>
              <span
                className={`font-overline-medium font-light ${
                  value === index ? "text-[#432FA7]" : "text-gray-400"
                }`}
              >
                {toPersianNumber(item.value)}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

RangeSlider.propTypes = {
  value: PropTypes.number,
  min: PropTypes.number,
  values: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    })
  ),
  onChange: PropTypes.func,
};

export default memo(RangeSlider);
