import { Instagram<PERSON><PERSON>, Rss, Telegram<PERSON>ogo, <PERSON>Logo } from "@phosphor-icons/react";
import use360requestStore from "store/360requestStore";
import OverviewBadges from "./OverviewBadges";

const AllPlatformContent = () => {
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info?.entity,
    statistical: state.report?.content?.report_info?.statistical,
  }));

  const data = sourceReport.statistical;
  const platforms = [
    {
      id: 3,
      title: "تعداد محتوای توییتر",
      count: data?.twitter?.find((item) => item.key === "post")?.count || "0",
      icon: XLogo,
      variant: "black",
      color: "white",
      subtitle: "تعداد منابع انتشار",
      subCount:
        data?.twitter?.find((item) => item.key === "source")?.count || "0",
    },
    {
      id: 2,
      title: "تعداد محتوای اینستاگرام",
      count: data?.instagram?.find((item) => item.key === "post")?.count || "0",
      icon: InstagramLogo,
      variant: "#e64787",
      color: "white",
      subtitle: "تعداد منابع انتشار",
      subCount:
        data?.instagram?.find((item) => item.key === "source")?.count || "0",
    },
    {
      id: 1,
      title: "تعداد محتوای تلگرام",
      count: data?.telegram?.find((item) => item.key === "post")?.count || "0",
      icon: TelegramLogo,
      variant: "#33AADD",
      color: "white",
      subtitle: "تعداد منابع انتشار",
      subCount:
        data?.telegram?.find((item) => item.key === "source")?.count || "0",
    },
    {
      id: 4,
      title: "تعداد محتوای اخبار",
      count: data?.news?.find((item) => item.key === "post")?.count || "0",
      icon: Rss,
      variant: "orange",
      color: "white",
      subtitle: "تعداد منابع انتشار",
      subCount: data?.news?.find((item) => item.key === "source")?.count || "0",
    },
  ];

  return (
    <div className="grid grid-cols-4 gap-4 w-full">
      {platforms.map((item) => (
        <OverviewBadges
          key={item.id}
          title={item.title}
          count={item.count}
          variant={item.variant}
          color={item.color}
          icon={item.icon}
          subtitle={item.subtitle}
          subCount={item.subCount}
        />
      ))}
    </div>
  );
};

export default AllPlatformContent;
